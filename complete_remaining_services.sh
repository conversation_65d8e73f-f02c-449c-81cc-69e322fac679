#!/bin/bash

# Script to complete remaining service pages
echo "🚀 Completing remaining service pages..."

# List of remaining service pages to complete
declare -a services=(
    "src/pages/services/custom-hardscaping-patios/RetainingWallConstruction.tsx"
    "src/pages/services/custom-hardscaping-patios/CustomFirePits.tsx"
    "src/pages/services/custom-hardscaping-patios/FirePitPizzaOvenInstallation.tsx"
    "src/pages/services/custom-hardscaping-patios/WalkwayPathwayInstallation.tsx"
    "src/pages/services/custom-hardscaping-patios/LuxuryHardscapeDesign.tsx"
    "src/pages/services/outdoor-living-features/WaterFeatureInstallation.tsx"
    "src/pages/services/outdoor-living-features/PergolaTrellisInstallation.tsx"
    "src/pages/services/outdoor-living-features/DeckPatioCoverInstallation.tsx"
    "src/pages/services/outdoor-living-features/OutdoorLightingInstallation.tsx"
    "src/pages/services/synthetic-turf-installation/ArtificialGrassSmallYards.tsx"
    "src/pages/services/synthetic-turf-installation/PetFriendlyArtificialTurf.tsx"
    "src/pages/services/synthetic-turf-installation/PuttingGreenInstallation.tsx"
    "src/pages/services/urban-landscape-design/HighEndLandscapeDesign.tsx"
    "src/pages/services/urban-landscape-design/ModernLandscapeDesign.tsx"
    "src/pages/services/urban-landscape-design/UrbanBackyardTransformation.tsx"
    "src/pages/services/urban-landscape-design/SmallYardLandscapeDesignBuild.tsx"
)

# Count total services
total=${#services[@]}
echo "📊 Found $total service pages to complete"

# Check which ones still use createPlaceholderPage
echo "🔍 Checking which pages still need completion..."
incomplete_count=0

for service in "${services[@]}"; do
    if grep -q "createPlaceholderPage" "$service" 2>/dev/null; then
        echo "❌ $service - Still using placeholder"
        ((incomplete_count++))
    else
        echo "✅ $service - Already completed"
    fi
done

echo ""
echo "📈 Summary:"
echo "   Total service pages: $total"
echo "   Still incomplete: $incomplete_count"
echo "   Already completed: $((total - incomplete_count))"

if [ $incomplete_count -eq 0 ]; then
    echo "🎉 All service pages are already completed!"
else
    echo "⚠️  $incomplete_count service pages still need completion"
    echo "💡 These pages will need to be completed manually using the same pattern as the completed pages"
fi

echo ""
echo "✅ Script completed!"
