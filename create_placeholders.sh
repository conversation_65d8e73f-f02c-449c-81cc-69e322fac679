#!/bin/bash

# Create remaining hardscaping services
services=(
  "LuxuryHardscapeDesign:Luxury Hardscape Design Boston:Luxury hardscape design and installation in Boston"
  "FirePitPizzaOvenInstallation:Fire Pit Pizza Oven Installation Boston:Custom fire pits and pizza ovens for outdoor entertaining"
  "CustomFirePits:Custom Fire Pits Boston:Bespoke fire pit design and installation"
  "WalkwayPathwayInstallation:Walkway Pathway Installation Boston:Beautiful walkways and pathways"
  "RetainingWallConstruction:Retaining Wall Construction Boston:Structural and decorative retaining walls"
)

for service in "${services[@]}"; do
  IFS=':' read -r filename title description <<< "$service"
  cat > "src/pages/services/custom-hardscaping-patios/${filename}.tsx" << EOFILE
import { createPlaceholderPage } from '../../../utils/createPlaceholderPage';

const ${filename} = createPlaceholderPage({
  title: "${title} | American Elm Landscape",
  description: "${description} in Boston area.",
  keywords: "${title,,}, Boston hardscaping",
  heroTitle: "${title}",
  heroSubtitle: "${description}",
  heroImage: "/images/hero-${filename,,}.jpg"
});

export default ${filename};
EOFILE
done

echo "Created hardscaping service pages"
