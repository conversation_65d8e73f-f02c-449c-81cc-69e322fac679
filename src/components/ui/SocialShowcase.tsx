import React from 'react';
import { motion } from 'framer-motion';
import { landscapeImages } from '../../utils/images';

interface SocialShowcaseProps {
  title?: string;
  subtitle?: string;
  className?: string;
}

const SocialShowcase: React.FC<SocialShowcaseProps> = ({
  title = "Follow Our Work",
  subtitle = "See our latest projects on social media",
  className = ""
}) => {
  const socialVideos = [
    {
      src: landscapeImages.videos.social1,
      title: "Backyard Transformation",
      description: "Watch this amazing transformation"
    },
    {
      src: landscapeImages.videos.social2,
      title: "Hardscape Installation",
      description: "Professional hardscaping work"
    },
    {
      src: landscapeImages.videos.social3,
      title: "Landscape Design",
      description: "Beautiful landscape design"
    }
  ];

  return (
    <section className={`py-16 ${className}`}>
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-4">
            {title}
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {subtitle}
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
          {socialVideos.map((video, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow"
            >
              <div className="aspect-[9/16] relative">
                <video
                  controls
                  className="w-full h-full object-cover"
                  poster={landscapeImages.hero.default}
                >
                  <source src={video.src} type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
              </div>
              <div className="p-4">
                <h3 className="font-serif font-bold text-dark-green mb-2">
                  {video.title}
                </h3>
                <p className="text-sm text-gray-600">
                  {video.description}
                </p>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center mt-12"
        >
          <p className="text-gray-600 mb-4">
            Follow us for more project updates and design inspiration
          </p>
          <div className="flex justify-center space-x-4">
            <a
              href="#"
              className="bg-dark-green text-white px-6 py-2 rounded-lg hover:bg-light-green transition-colors"
            >
              Follow on Instagram
            </a>
            <a
              href="#"
              className="border border-dark-green text-dark-green px-6 py-2 rounded-lg hover:bg-dark-green hover:text-white transition-colors"
            >
              View Portfolio
            </a>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default SocialShowcase;
