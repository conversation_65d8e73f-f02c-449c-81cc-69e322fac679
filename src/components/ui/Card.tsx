import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import Button from './Button';

interface CardProps {
  title: string;
  description: string;
  image: string;
  imageAlt: string;
  link: string;
  ctaText?: string;
  className?: string;
}

const Card: React.FC<CardProps> = ({
  title,
  description,
  image,
  imageAlt,
  link,
  ctaText = 'Learn More',
  className = ''
}) => {
  return (
    <motion.div
      whileHover={{ y: -5 }}
      transition={{ duration: 0.3 }}
      className={`bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 ${className}`}
    >
      <Link to={link} className="block">
        <div className="relative overflow-hidden">
          <img
            src={image}
            alt={imageAlt}
            className="w-full h-48 object-cover transition-transform duration-300 hover:scale-105"
            loading="lazy"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300" />
        </div>
      </Link>
      
      <div className="p-6">
        <Link to={link}>
          <h3 className="text-xl font-serif font-semibold text-dark-green mb-3 hover:text-light-green transition-colors">
            {title}
          </h3>
        </Link>
        
        <p className="text-gray-600 mb-4 leading-relaxed">
          {description}
        </p>
        
        <Button to={link} variant="outline" size="sm">
          {ctaText}
        </Button>
      </div>
    </motion.div>
  );
};

export default Card;
