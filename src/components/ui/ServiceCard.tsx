import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ArrowRight } from 'lucide-react';

interface ServiceCardProps {
  title: string;
  description: string;
  link: string;
  icon?: React.ReactNode;
  className?: string;
}

const ServiceCard: React.FC<ServiceCardProps> = ({
  title,
  description,
  link,
  icon,
  className = ''
}) => {
  return (
    <motion.div
      whileHover={{ y: -5 }}
      transition={{ duration: 0.3 }}
      className={`bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300 h-full flex flex-col ${className}`}
    >
      {icon && (
        <div className="mb-4 text-dark-green">
          {icon}
        </div>
      )}
      
      <Link to={link}>
        <h3 className="text-xl font-serif font-semibold text-dark-green mb-3 hover:text-light-green transition-colors">
          {title}
        </h3>
      </Link>
      
      <p className="text-gray-600 mb-4 leading-relaxed flex-grow">
        {description}
      </p>
      
      <Link
        to={link}
        className="inline-flex items-center text-dark-green font-semibold hover:text-light-green transition-colors group"
      >
        Learn More
        <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
      </Link>
    </motion.div>
  );
};

export default ServiceCard;

