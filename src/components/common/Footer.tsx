import { Link } from 'react-router-dom';

const Footer: React.FC = () => {
  const services = [
    { name: 'Urban Landscape Design', path: '/urban-landscape-design' },
    { name: 'Custom Hardscaping & Patios', path: '/custom-hardscaping-patios' },
    { name: 'Synthetic Turf Installation', path: '/synthetic-turf-installation' },
    { name: 'Outdoor Living Features', path: '/outdoor-living-features' },
  ];

  const locations = [
    { name: 'Boston', path: '/locations/boston/landscape-designer-back-bay' },
    { name: 'Cambridge', path: '/locations/cambridge/landscape-designer' },
    { name: 'Somerville', path: '/locations/somerville/landscape-designer' },
    { name: 'Brookline', path: '/locations/brookline/landscape-designer' },
    { name: 'Arlington', path: '/locations/arlington/landscape-designer' },
    { name: 'Winchester', path: '/locations/winchester/landscape-designer' },
    { name: 'Medford', path: '/locations/medford/landscape-designer' },
  ];

  const urbanDesignServices = [
    { name: 'Outdoor Living Space Design', path: '/urban-landscape-design/outdoor-living-space-design' },
    { name: 'Urban Backyard Transformation', path: '/urban-landscape-design/urban-backyard-transformation' },
    { name: 'Small Yard Landscape Design Build', path: '/urban-landscape-design/small-yard-landscape-design-build' },
    { name: 'High End Landscape Design', path: '/urban-landscape-design/high-end-landscape-design' },
    { name: 'Modern Landscape Design', path: '/urban-landscape-design/modern-landscape-design' },
  ];

  return (
    <footer className="bg-dark-green text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div>
            <h3 className="text-xl font-serif font-bold mb-4">American Elm Landscape</h3>
            <p className="text-gray-300 mb-4">
              High-end urban landscape design and build firm serving Greater Boston with luxury outdoor living solutions.
            </p>
            <div className="space-y-2 text-sm text-gray-300">
              <div>📍 Medford, MA</div>
              <div>📞 (617) 555-0123</div>
              <div>✉️ <EMAIL></div>
            </div>
          </div>

          {/* Main Services */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Our Services</h4>
            <ul className="space-y-2">
              {services.map((service) => (
                <li key={service.path}>
                  <Link 
                    to={service.path} 
                    className="text-gray-300 hover:text-white transition-colors text-sm"
                  >
                    {service.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Urban Design Services */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Urban Design</h4>
            <ul className="space-y-2">
              {urbanDesignServices.map((service) => (
                <li key={service.path}>
                  <Link 
                    to={service.path} 
                    className="text-gray-300 hover:text-white transition-colors text-sm"
                  >
                    {service.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Service Areas */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Service Areas</h4>
            <ul className="space-y-2">
              {locations.map((location) => (
                <li key={location.path}>
                  <Link 
                    to={location.path} 
                    className="text-gray-300 hover:text-white transition-colors text-sm"
                  >
                    {location.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Additional Links */}
        <div className="border-t border-light-green mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex flex-wrap gap-6 mb-4 md:mb-0">
              <Link to="/about" className="text-gray-300 hover:text-white transition-colors text-sm">
                About Us
              </Link>
              <Link to="/contact" className="text-gray-300 hover:text-white transition-colors text-sm">
                Contact
              </Link>
              <Link to="/blog" className="text-gray-300 hover:text-white transition-colors text-sm">
                Blog
              </Link>
              <Link to="/privacy" className="text-gray-300 hover:text-white transition-colors text-sm">
                Privacy Policy
              </Link>
              <Link to="/terms" className="text-gray-300 hover:text-white transition-colors text-sm">
                Terms of Service
              </Link>
            </div>
            
            {/* Social Media */}
            <div className="flex space-x-4">
              <a href="#" className="text-gray-300 hover:text-white transition-colors" aria-label="Facebook">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <a href="#" className="text-gray-300 hover:text-white transition-colors" aria-label="Instagram">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297z"/>
                </svg>
              </a>
              <a href="#" className="text-gray-300 hover:text-white transition-colors" aria-label="LinkedIn">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
              </a>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="border-t border-light-green mt-8 pt-8 text-center">
          <p className="text-gray-300 text-sm">
            © {new Date().getFullYear()} American Elm Landscape. All rights reserved. 
            Professional landscape design and build services in Greater Boston.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
