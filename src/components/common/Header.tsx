import { useState } from 'react';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { landscapeImages } from '../../utils/images';

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  const services = [
    { name: 'Urban Landscape Design', path: '/urban-landscape-design' },
    { name: 'Custom Hardscaping & Patios', path: '/custom-hardscaping-patios' },
    { name: 'Synthetic Turf Installation', path: '/synthetic-turf-installation' },
    { name: 'Outdoor Living Features', path: '/outdoor-living-features' },
  ];

  const locations = [
    { name: 'Boston', path: '/locations/boston/landscape-designer-back-bay' },
    { name: 'Cambridge', path: '/locations/cambridge/landscape-designer' },
    { name: 'Somerville', path: '/locations/somerville/landscape-designer' },
    { name: 'Brook<PERSON>', path: '/locations/brookline/landscape-designer' },
    { name: 'Arlington', path: '/locations/arlington/landscape-designer' },
    { name: 'Winchester', path: '/locations/winchester/landscape-designer' },
    { name: 'Medford', path: '/locations/medford/landscape-designer' },
  ];

  return (
    <header className="bg-dark-green text-white sticky top-0 z-50 shadow-lg">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <img
              src={landscapeImages.logo}
              alt="American Elm Landscape"
              className="h-10 w-10"
            />
            <div className="text-xl font-serif font-bold">
              American Elm Landscape
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link to="/" className="hover:text-gray-300 transition-colors">
              Home
            </Link>
            
            {/* Services Dropdown */}
            <div 
              className="relative"
              onMouseEnter={() => setActiveDropdown('services')}
              onMouseLeave={() => setActiveDropdown(null)}
            >
              <button className="hover:text-gray-300 transition-colors flex items-center">
                Services
                <svg className="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              
              <AnimatePresence>
                {activeDropdown === 'services' && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full left-0 mt-2 w-64 bg-white text-dark-green rounded-lg shadow-xl py-2"
                  >
                    {services.map((service) => (
                      <Link
                        key={service.path}
                        to={service.path}
                        className="block px-4 py-2 hover:bg-gray-100 transition-colors"
                      >
                        {service.name}
                      </Link>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Locations Dropdown */}
            <div 
              className="relative"
              onMouseEnter={() => setActiveDropdown('locations')}
              onMouseLeave={() => setActiveDropdown(null)}
            >
              <button className="hover:text-gray-300 transition-colors flex items-center">
                Locations
                <svg className="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              
              <AnimatePresence>
                {activeDropdown === 'locations' && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full left-0 mt-2 w-48 bg-white text-dark-green rounded-lg shadow-xl py-2"
                  >
                    {locations.map((location) => (
                      <Link
                        key={location.path}
                        to={location.path}
                        className="block px-4 py-2 hover:bg-gray-100 transition-colors"
                      >
                        {location.name}
                      </Link>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            <Link to="/about" className="hover:text-gray-300 transition-colors">
              About
            </Link>
            <Link to="/contact" className="hover:text-gray-300 transition-colors">
              Contact
            </Link>
            
            <Link 
              to="/contact" 
              className="bg-white text-dark-green px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors font-medium"
            >
              Get Quote
            </Link>
          </nav>

          {/* Mobile menu button */}
          <button
            onClick={toggleMenu}
            className="md:hidden p-2"
            aria-label="Toggle menu"
          >
            <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              {isMenuOpen ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              )}
            </svg>
          </button>
        </div>

        {/* Mobile Navigation */}
        <AnimatePresence>
          {isMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="md:hidden py-4 border-t border-light-green"
            >
              <div className="flex flex-col space-y-4">
                <Link to="/" className="hover:text-gray-300 transition-colors" onClick={toggleMenu}>
                  Home
                </Link>
                
                <div>
                  <div className="font-medium mb-2">Services</div>
                  <div className="pl-4 space-y-2">
                    {services.map((service) => (
                      <Link
                        key={service.path}
                        to={service.path}
                        className="block text-sm hover:text-gray-300 transition-colors"
                        onClick={toggleMenu}
                      >
                        {service.name}
                      </Link>
                    ))}
                  </div>
                </div>

                <div>
                  <div className="font-medium mb-2">Locations</div>
                  <div className="pl-4 space-y-2">
                    {locations.map((location) => (
                      <Link
                        key={location.path}
                        to={location.path}
                        className="block text-sm hover:text-gray-300 transition-colors"
                        onClick={toggleMenu}
                      >
                        {location.name}
                      </Link>
                    ))}
                  </div>
                </div>

                <Link to="/about" className="hover:text-gray-300 transition-colors" onClick={toggleMenu}>
                  About
                </Link>
                <Link to="/contact" className="hover:text-gray-300 transition-colors" onClick={toggleMenu}>
                  Contact
                </Link>
                
                <Link 
                  to="/contact" 
                  className="bg-white text-dark-green px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors font-medium text-center"
                  onClick={toggleMenu}
                >
                  Get Quote
                </Link>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </header>
  );
};

export default Header;
