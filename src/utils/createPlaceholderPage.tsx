import SEO from '../components/common/SEO';
import Hero from '../components/common/Hero';
import { getImageByCategory } from './images';

interface PlaceholderPageProps {
  title: string;
  description: string;
  keywords: string;
  heroTitle: string;
  heroSubtitle: string;
  heroImage: string;
}

export const createPlaceholderPage = ({
  title,
  description,
  keywords,
  heroTitle,
  heroSubtitle,
  heroImage
}: PlaceholderPageProps) => {
  const PlaceholderPage: React.FC = () => {
    return (
      <>
        <SEO
          title={title}
          description={description}
          keywords={keywords}
        />

        <Hero
          title={heroTitle}
          subtitle={heroSubtitle}
          backgroundImage={heroImage || getImageByCategory(heroTitle)}
          ctaText="Get Consultation"
          ctaLink="/contact"
          height="medium"
        />

        <div className="container mx-auto px-4 py-16">
          <div className="text-center max-w-2xl mx-auto">
            <h2 className="text-3xl font-serif font-bold text-dark-green mb-6">
              Page Under Construction
            </h2>
            <p className="text-lg text-gray-700 mb-8">
              This page is currently being developed. Please visit our homepage to explore our services 
              or contact us directly for information about this specific service.
            </p>
            <div className="space-y-4">
              <a 
                href="/contact" 
                className="inline-block bg-dark-green text-white px-8 py-3 rounded-lg hover:bg-light-green transition-colors mr-4"
              >
                Contact Us
              </a>
              <a 
                href="/" 
                className="inline-block border-2 border-dark-green text-dark-green px-8 py-3 rounded-lg hover:bg-dark-green hover:text-white transition-colors"
              >
                Return Home
              </a>
            </div>
          </div>
        </div>
      </>
    );
  };

  return PlaceholderPage;
};
