// American Elm Landscape real project images and fallback images
export const landscapeImages = {
  // Hero images (1920x1080) - Using real American Elm Landscape projects
  hero: {
    default: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1920/u_https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df238b9a13eb2fbb54f7b4.png',
    urbanDesign: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1920/u_https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df238b9a13eb2fbb54f7b4.png',
    hardscaping: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1920/u_https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df238b897ef525cd9e0da7.png',
    syntheticTurf: 'https://images.unsplash.com/photo-1585320806297-9794b3e4eeae?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
    outdoorLiving: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1920/u_https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df238b897ef525cd9e0da7.png',
    patio: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1920/u_https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df238b897ef525cd9e0da7.png',
    firepit: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1920/u_https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df238b9a13eb2fbb54f7b4.png',
    modernDesign: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1920/u_https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df238b9a13eb2fbb54f7b4.png',
    smallYard: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1920/u_https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df238b9a13eb2fbb54f7b4.png',
    luxuryDesign: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1920/u_https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df238b897ef525cd9e0da7.png',
    waterFeature: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1920/u_https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df238b9a13eb2fbb54f7b4.png',
    outdoorKitchen: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1920/u_https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df238b897ef525cd9e0da7.png',
    puttingGreen: 'https://images.unsplash.com/photo-1585320806297-9794b3e4eeae?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80'
  },
  
  // Gallery images (800x600) - Using real American Elm Landscape projects
  gallery: {
    backyard: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_800/u_https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df238b9a13eb2fbb54f7b4.png',
    patio: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_800/u_https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df238b897ef525cd9e0da7.png',
    turf: 'https://images.unsplash.com/photo-1585320806297-9794b3e4eeae?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    kitchen: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_800/u_https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df238b897ef525cd9e0da7.png',
    modern: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_800/u_https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df238b9a13eb2fbb54f7b4.png',
    water: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_800/u_https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df238b9a13eb2fbb54f7b4.png'
  },

  // Video URLs for project showcases
  videos: {
    project1: 'https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df23dc85bf794407368e2e.mp4',
    project2: 'https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df23dc065f28bdd4e9d55c.mp4',
    social1: 'https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df14dad6c63e13098f4f72.mp4',
    social2: 'https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df14da897ef500759c3d09.mp4',
    social3: 'https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df14dabd01df78ad7652bf.mp4'
  },

  // Company logo
  logo: 'https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df16a4bd01dff6277689e0.png'
};

// Helper function to get a random landscape image
export const getRandomLandscapeImage = (type: 'hero' | 'gallery' = 'hero'): string => {
  const images = Object.values(landscapeImages[type]);
  return images[Math.floor(Math.random() * images.length)];
};

// Helper function to get image by category
export const getImageByCategory = (category: string): string => {
  const categoryMap: { [key: string]: string } = {
    'urban': landscapeImages.hero.urbanDesign,
    'hardscaping': landscapeImages.hero.hardscaping,
    'patio': landscapeImages.hero.patio,
    'firepit': landscapeImages.hero.firepit,
    'fire': landscapeImages.hero.firepit,
    'synthetic': landscapeImages.hero.syntheticTurf,
    'turf': landscapeImages.hero.syntheticTurf,
    'outdoor': landscapeImages.hero.outdoorLiving,
    'kitchen': landscapeImages.hero.outdoorKitchen,
    'modern': landscapeImages.hero.modernDesign,
    'luxury': landscapeImages.hero.luxuryDesign,
    'small': landscapeImages.hero.smallYard,
    'water': landscapeImages.hero.waterFeature,
    'putting': landscapeImages.hero.puttingGreen,
    'green': landscapeImages.hero.puttingGreen
  };

  // Find matching category or return default
  const lowerCategory = category.toLowerCase();
  for (const [key, image] of Object.entries(categoryMap)) {
    if (lowerCategory.includes(key)) {
      return image;
    }
  }
  
  return landscapeImages.hero.default;
};
