import { company } from './constants';

export const createLocalBusinessSchema = () => ({
  "@context": "https://schema.org",
  "@type": "LocalBusiness",
  "name": company.name,
  "description": company.description,
  "url": "https://americanelmlandscape.com",
  "telephone": company.phone,
  "email": company.email,
  "address": {
    "@type": "PostalAddress",
    "addressLocality": "Medford",
    "addressRegion": "MA",
    "addressCountry": "US"
  },
  "geo": {
    "@type": "GeoCoordinates",
    "latitude": "42.4184",
    "longitude": "-71.1061"
  },
  "openingHours": [
    "Mo-Fr 08:00-18:00",
    "Sa 09:00-16:00"
  ],
  "serviceArea": {
    "@type": "GeoCircle",
    "geoMidpoint": {
      "@type": "GeoCoordinates",
      "latitude": "42.4184",
      "longitude": "-71.1061"
    },
    "geoRadius": "25000"
  },
  "hasOfferCatalog": {
    "@type": "OfferCatalog",
    "name": "Landscape Design Services",
    "itemListElement": [
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Urban Landscape Design",
          "description": "Professional landscape design services for urban properties"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Custom Hardscaping & Patios",
          "description": "Custom hardscape design and patio installation"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Synthetic Turf Installation",
          "description": "Professional artificial grass and synthetic turf installation"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Outdoor Living Features",
          "description": "Custom outdoor kitchens, lighting, and water features"
        }
      }
    ]
  },
  "priceRange": "$$$-$$$$",
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "4.9",
    "reviewCount": "127"
  }
});

export const createServiceSchema = (serviceName: string, description: string, location?: string) => ({
  "@context": "https://schema.org",
  "@type": "Service",
  "name": serviceName,
  "description": description,
  "provider": {
    "@type": "LocalBusiness",
    "name": company.name,
    "telephone": company.phone,
    "email": company.email,
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "Medford",
      "addressRegion": "MA",
      "addressCountry": "US"
    }
  },
  "areaServed": location ? {
    "@type": "City",
    "name": location,
    "containedInPlace": {
      "@type": "State",
      "name": "Massachusetts"
    }
  } : {
    "@type": "State",
    "name": "Massachusetts"
  },
  "offers": {
    "@type": "Offer",
    "priceRange": "$$$-$$$$",
    "availability": "https://schema.org/InStock"
  }
});

export const createBreadcrumbSchema = (items: Array<{ name: string; url: string }>) => ({
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": items.map((item, index) => ({
    "@type": "ListItem",
    "position": index + 1,
    "name": item.name,
    "item": item.url
  }))
});
