import { motion } from 'framer-motion';
import { Phone, Mail, MapPin, Clock, Trophy, Leaf, Star } from 'lucide-react';
import SEO from '../components/common/SEO';
import Hero from '../components/common/Hero';
import ContactForm from '../components/forms/ContactForm';
import { createBreadcrumbSchema } from '../utils/schema';
import { landscapeImages } from '../utils/images';

const Contact: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Contact', url: 'https://americanelmlandscape.com/contact' }
  ];

  const contactMethods = [
    { icon: Phone, title: 'Phone', detail: '(*************', description: 'Call us Monday-Friday, 8am-6pm' },
    { icon: Mail, title: 'Email', detail: '<EMAIL>', description: 'We respond within 24 hours' },
    { icon: MapPin, title: 'Service Area', detail: 'Greater Boston', description: 'Cambridge, Brookline, Back Bay, South End & more' },
    { icon: Clock, title: 'Hours', detail: 'Mon-Fri: 8am-6pm', description: 'Saturday by appointment' }
  ];

  const processSteps = [
    { step: '1', title: 'Initial Consultation', description: 'Free consultation to discuss your vision, needs, and budget' },
    { step: '2', title: 'Site Analysis', description: 'Comprehensive evaluation of your property and design opportunities' },
    { step: '3', title: 'Custom Design', description: 'Detailed design proposal with renderings and material selections' },
    { step: '4', title: 'Professional Installation', description: 'Expert installation with dedicated project management' }
  ];

  return (
    <>
      <SEO
        title="Contact American Elm Landscape | Free Consultation | Boston Landscape Design"
        description="Contact American Elm Landscape for your Boston landscape design project. Free consultation, professional service, and expert design solutions for urban properties."
        keywords="contact American Elm Landscape, Boston landscape design consultation, landscape design quote, free consultation"
        canonical="https://americanelmlandscape.com/contact"
        schema={[createBreadcrumbSchema(breadcrumbs)]}
      />

      <Hero
        title="Contact Us"
        subtitle="Ready to Transform Your Outdoor Space? Let's Talk."
        backgroundImage={landscapeImages.hero.contact}
        height="small"
      />

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h1 className="text-4xl md:text-5xl font-serif font-bold text-dark-green mb-6">
                Get Started with Your Landscape Project
              </h1>
              <p className="text-xl text-gray-700 max-w-3xl mx-auto">
                Whether you're planning a complete landscape transformation or a specific outdoor feature, we're here to help.
                Contact us today for a free consultation and let's discuss how we can bring your vision to life.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
              {contactMethods.map((method, index) => {
                const IconComponent = method.icon;
                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="text-center bg-gray-50 p-6 rounded-lg"
                  >
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-dark-green/10 rounded-full mb-4">
                      <IconComponent className="w-8 h-8 text-dark-green" />
                    </div>
                    <h3 className="text-xl font-serif font-semibold text-dark-green mb-2">{method.title}</h3>
                    <p className="text-lg font-semibold text-gray-800 mb-2">{method.detail}</p>
                    <p className="text-sm text-gray-600">{method.description}</p>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
            >
              Our Process
            </motion.h2>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {processSteps.map((process, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="text-center"
                >
                  <div className="w-16 h-16 bg-dark-green rounded-full flex items-center justify-center mx-auto mb-4 text-white text-2xl font-bold">
                    {process.step}
                  </div>
                  <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">{process.title}</h3>
                  <p className="text-gray-700">{process.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6">
                Request a Free Consultation
              </h2>
              <p className="text-lg text-gray-700">
                Fill out the form below and we'll get back to you within 24 hours to schedule your free consultation.
              </p>
            </motion.div>

            <ContactForm />
          </div>
        </div>
      </section>

      <section className="py-16 bg-dark-green text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-3xl font-serif font-bold mb-6">Why Choose American Elm Landscape?</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-8">
                <div>
                  <div className="inline-flex items-center justify-center w-20 h-20 bg-white/10 rounded-full mb-4">
                    <Trophy className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="text-xl font-serif font-bold mb-3">Expert Design</h3>
                  <p className="text-gray-200">Professional landscape designers with years of Boston-area experience</p>
                </div>
                <div>
                  <div className="inline-flex items-center justify-center w-20 h-20 bg-white/10 rounded-full mb-4">
                    <Leaf className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="text-xl font-serif font-bold mb-3">Quality Craftsmanship</h3>
                  <p className="text-gray-200">Meticulous attention to detail and premium materials in every project</p>
                </div>
                <div>
                  <div className="inline-flex items-center justify-center w-20 h-20 bg-white/10 rounded-full mb-4">
                    <Star className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="text-xl font-serif font-bold mb-3">Client Satisfaction</h3>
                  <p className="text-gray-200">Dedicated to exceeding expectations and building lasting relationships</p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Contact;
