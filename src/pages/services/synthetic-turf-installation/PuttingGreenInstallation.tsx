import { motion } from 'framer-motion';
import { <PERSON> } from 'react-router-dom';
import { Flag, Target, Star, Sparkles } from 'lucide-react';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Gallery from '../../../components/ui/Gallery';
import ContactForm from '../../../components/forms/ContactForm';
import Button from '../../../components/ui/Button';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { services } from '../../../utils/constants';
import { landscapeImages } from '../../../utils/images';

const PuttingGreenInstallation: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Synthetic Turf Installation', url: 'https://americanelmlandscape.com/synthetic-turf-installation' },
    { name: 'Putting Green Installation Boston', url: 'https://americanelmlandscape.com/synthetic-turf-installation/putting-green-installation' }
  ];

  const features = [
    { title: 'Custom Design', description: 'Tailored layouts with multiple holes, breaks, and undulations to match your skill level', icon: Flag },
    { title: 'Professional Turf', description: 'Tour-quality synthetic turf with realistic ball roll and consistent putting surface', icon: Target },
    { title: 'Year-Round Use', description: 'Practice your short game 365 days a year, regardless of Boston weather', icon: Star },
    { title: 'Low Maintenance', description: 'No mowing, watering, or fertilizing—just pure putting enjoyment', icon: Sparkles }
  ];

  const customOptions = [
    'Multiple hole locations',
    'Custom shapes and sizes',
    'Undulations and breaks',
    'Fringe and rough areas',
    'Chipping areas',
    'Sand bunkers',
    'Integrated lighting',
    'Ball return systems'
  ];

  const galleryImages = [
    { src: landscapeImages.gallery.puttingGreen, alt: 'Custom putting green Boston', caption: 'Backyard putting green in Brookline' },
    { src: landscapeImages.gallery.golfGreen, alt: 'Professional putting green', caption: 'Tour-quality synthetic turf' },
    { src: landscapeImages.gallery.backyardGolf, alt: 'Backyard golf green', caption: 'Custom design with multiple holes' },
    { src: landscapeImages.gallery.puttingPractice, alt: 'Putting practice area', caption: 'Year-round practice facility' }
  ];

  const relatedServices = services.syntheticTurf.filter(service => service.name !== 'Putting Green Installation').slice(0, 3);

  return (
    <>
      <SEO
        title="Putting Green Installation Boston | Custom Backyard Golf Greens | American Elm Landscape"
        description="Professional putting green installation in Boston. Custom backyard golf greens with premium synthetic turf for year-round practice and entertainment."
        keywords="putting green installation Boston, backyard putting green, custom golf green, synthetic putting green, artificial golf turf, home putting green"
        canonical="https://americanelmlandscape.com/synthetic-turf-installation/putting-green-installation"
        schema={[
          createServiceSchema('Putting Green Installation Boston', 'Professional putting green installation services for Boston properties, creating custom backyard golf greens for year-round practice.', 'Boston'),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      <Hero title="Putting Green Installation Boston" subtitle="Custom Backyard Golf Greens for Year-Round Practice" backgroundImage={landscapeImages.hero.puttingGreen} ctaText="Design Your Green" ctaLink="/contact" height="medium" />

      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <Link to="/synthetic-turf-installation" className="text-dark-green hover:text-light-green">Synthetic Turf Installation</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Putting Green Installation Boston</span>
          </nav>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h1 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-4xl md:text-5xl font-serif font-bold text-dark-green mb-8">
              Putting Green Installation Boston
            </motion.h1>
            <motion.div initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: 0.2 }} className="prose prose-lg max-w-none text-gray-700 mb-12">
              <p className="text-xl leading-relaxed mb-6">
                Transform your Boston backyard into a personal golf practice facility with professional putting green installation. Our custom-designed golf greens feature tour-quality synthetic turf, realistic ball roll, and personalized layouts that help you improve your short game while adding unique entertainment value to your property.
              </p>
              <h2 className="text-2xl font-serif font-semibold text-dark-green mb-4">Tour-Quality Greens for Your Backyard</h2>
              <p className="leading-relaxed mb-6">
                We install professional-grade putting greens using the same high-quality synthetic turf found at top golf facilities. Our greens feature realistic ball roll, consistent speed, and customizable undulations that challenge your putting skills. Whether you want a simple practice green or an elaborate multi-hole layout with breaks and slopes, we create designs tailored to your space and skill level.
              </p>
              <p className="leading-relaxed mb-6">
                Our installation process includes proper base preparation, precise grading for drainage and ball roll, professional turf installation, and custom features like multiple cup locations, fringe areas, and even chipping zones. The result is a putting green that performs beautifully year-round, regardless of Boston's weather, with minimal maintenance required.
              </p>
              <h3 className="text-xl font-semibold text-dark-green mb-4">Practice Anytime, Improve Your Game</h3>
              <p className="leading-relaxed">
                With a backyard putting green, you can practice your short game whenever you want—early morning, late evening, or even during Boston's long winters. Consistent practice on a quality surface helps lower your scores while providing entertainment for family and friends. It's an investment in your game and your property value.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12">
            Putting Green Features
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <motion.div key={index} initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="text-center bg-white p-6 rounded-lg shadow-lg">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-dark-green/10 rounded-full mb-4">
                    <IconComponent className="w-8 h-8 text-dark-green" />
                  </div>
                  <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">{feature.title}</h3>
                  <p className="text-gray-700">{feature.description}</p>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-8">
              Custom Design Options
            </motion.h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {customOptions.map((option, index) => (
                <motion.div key={index} initial={{ opacity: 0, x: -20 }} whileInView={{ opacity: 1, x: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="flex items-start">
                  <svg className="w-6 h-6 text-dark-green mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-lg text-gray-700">{option}</span>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12">
            Putting Green Portfolio
          </motion.h2>
          <Gallery images={galleryImages} />
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl font-serif font-bold text-dark-green text-center mb-12">
            Related Synthetic Turf Services
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {relatedServices.map((service, index) => (
              <motion.div key={service.path} initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="bg-white rounded-lg shadow-lg p-6 text-center">
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">{service.name} Boston</h3>
                <p className="text-gray-700 mb-4">{service.description}</p>
                <Button to={service.path} variant="outline" size="sm">Learn More</Button>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6">Ready for Your Putting Green?</h2>
              <p className="text-lg text-gray-700">Contact us for a free consultation and custom design for your backyard putting green.</p>
            </motion.div>
            <ContactForm prefilledService="Putting Green Installation" />
          </div>
        </div>
      </section>
    </>
  );
};

export default PuttingGreenInstallation;
