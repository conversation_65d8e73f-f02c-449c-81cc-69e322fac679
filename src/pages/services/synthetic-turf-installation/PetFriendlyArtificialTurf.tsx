import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Gallery from '../../../components/ui/Gallery';
import ContactForm from '../../../components/forms/ContactForm';
import Button from '../../../components/ui/Button';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { services } from '../../../utils/constants';
import { landscapeImages } from '../../../utils/images';

const PetFriendlyArtificialTurf: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Synthetic Turf Installation', url: 'https://americanelmlandscape.com/synthetic-turf-installation' },
    { name: 'Pet Friendly Artificial Turf Boston', url: 'https://americanelmlandscape.com/synthetic-turf-installation/pet-friendly-artificial-turf' }
  ];

  const petFeatures = [
    { title: 'Superior Drainage', description: 'Advanced drainage system prevents odors and keeps surface clean and dry', icon: '💧' },
    { title: 'Antimicrobial Protection', description: 'Built-in antimicrobial technology inhibits bacteria growth for healthier pet areas', icon: '🛡️' },
    { title: 'Durable & Safe', description: 'Non-toxic, lead-free materials that withstand heavy pet use and digging', icon: '🐾' },
    { title: 'Easy Cleaning', description: 'Simple to rinse and maintain, eliminating muddy paws and brown spots', icon: '✨' }
  ];

  const benefits = [
    'No more muddy paws or dirt tracked indoors',
    'Eliminates brown spots from pet urine',
    'No digging holes or destroyed grass',
    'Easy waste cleanup and maintenance',
    'Safe, non-toxic materials for pets',
    'Excellent drainage prevents odors',
    'Durable enough for active dogs',
    'Year-round green, usable surface'
  ];

  const galleryImages = [
    { src: landscapeImages.gallery.petTurf, alt: 'Pet friendly artificial turf Boston', caption: 'Safe synthetic turf for dogs' },
    { src: landscapeImages.gallery.dogYard, alt: 'Dog-friendly backyard turf', caption: 'Durable turf for active pets' },
    { src: landscapeImages.gallery.petArea, alt: 'Pet area with artificial grass', caption: 'Clean, low-maintenance pet space' },
    { src: landscapeImages.gallery.happyDog, alt: 'Dog on artificial turf', caption: 'Happy pets love synthetic grass' }
  ];

  const relatedServices = services.syntheticTurf.filter(service => service.name !== 'Pet Friendly Artificial Turf').slice(0, 3);

  return (
    <>
      <SEO
        title="Pet Friendly Artificial Turf Boston | Safe & Durable | American Elm Landscape"
        description="Pet-friendly artificial turf installation in Boston. Safe, durable, and easy-to-clean synthetic grass for pet areas. Antimicrobial protection and superior drainage."
        keywords="pet friendly artificial turf Boston, dog-safe synthetic grass, pet turf installation, dog-friendly artificial grass, pet area turf"
        canonical="https://americanelmlandscape.com/synthetic-turf-installation/pet-friendly-artificial-turf"
        schema={[
          createServiceSchema('Pet Friendly Artificial Turf Boston', 'Professional pet-friendly artificial turf installation for Boston properties, providing safe, durable, and easy-to-maintain surfaces for pets.', 'Boston'),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      <Hero title="Pet Friendly Artificial Turf Boston" subtitle="Safe and Durable Synthetic Grass for Your Pets" backgroundImage={landscapeImages.hero.petTurf} ctaText="Get Free Quote" ctaLink="/contact" height="medium" />

      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <Link to="/synthetic-turf-installation" className="text-dark-green hover:text-light-green">Synthetic Turf Installation</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Pet Friendly Artificial Turf Boston</span>
          </nav>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h1 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-4xl md:text-5xl font-serif font-bold text-dark-green mb-8">
              Pet Friendly Artificial Turf Boston
            </motion.h1>
            <motion.div initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: 0.2 }} className="prose prose-lg max-w-none text-gray-700 mb-12">
              <p className="text-xl leading-relaxed mb-6">
                Create the perfect outdoor space for your pets with professional pet-friendly artificial turf installation. Our specialized synthetic grass is designed specifically for pet areas, featuring superior drainage, antimicrobial protection, and durable construction that stands up to active dogs while providing a clean, safe surface year-round.
              </p>
              <h2 className="text-2xl font-serif font-semibold text-dark-green mb-4">Designed Specifically for Pets</h2>
              <p className="leading-relaxed mb-6">
                Not all artificial grass is suitable for pets. Our pet-friendly turf features advanced drainage systems that quickly channel liquids away, antimicrobial backing that inhibits bacteria growth, and non-toxic materials that are completely safe for your furry friends. Say goodbye to muddy paws, brown spots, and destroyed grass—our synthetic turf stays green and clean no matter how much your pets use it.
              </p>
              <p className="leading-relaxed mb-6">
                We install pet turf throughout Boston's urban neighborhoods, helping dog owners create beautiful, functional outdoor spaces that both pets and people can enjoy. Our installations include proper base preparation, advanced drainage systems, and professional finishing that ensures long-lasting performance and easy maintenance.
              </p>
              <h3 className="text-xl font-semibold text-dark-green mb-4">Clean, Safe, and Low-Maintenance</h3>
              <p className="leading-relaxed">
                Pet-friendly artificial turf eliminates the constant battle with mud, dirt, and damaged grass. Your pets get a comfortable, safe surface to play on, and you get a beautiful yard that stays clean and green with minimal maintenance. Simple rinsing keeps the surface fresh, and waste cleanup is easier than ever.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12">
            Pet-Friendly Features
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {petFeatures.map((feature, index) => (
              <motion.div key={index} initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="text-center bg-white p-6 rounded-lg shadow-lg">
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">{feature.title}</h3>
                <p className="text-gray-700">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-8">
              Benefits for Pet Owners
            </motion.h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {benefits.map((benefit, index) => (
                <motion.div key={index} initial={{ opacity: 0, x: -20 }} whileInView={{ opacity: 1, x: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="flex items-start">
                  <svg className="w-6 h-6 text-dark-green mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-lg text-gray-700">{benefit}</span>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12">
            Pet Turf Installations
          </motion.h2>
          <Gallery images={galleryImages} />
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl font-serif font-bold text-dark-green text-center mb-12">
            Related Synthetic Turf Services
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {relatedServices.map((service, index) => (
              <motion.div key={service.path} initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="bg-white rounded-lg shadow-lg p-6 text-center">
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">{service.name} Boston</h3>
                <p className="text-gray-700 mb-4">{service.description}</p>
                <Button to={service.path} variant="outline" size="sm">Learn More</Button>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6">Ready for Pet-Friendly Turf?</h2>
              <p className="text-lg text-gray-700">Contact us for a free consultation and quote for your pet-friendly artificial turf installation.</p>
            </motion.div>
            <ContactForm prefilledService="Pet Friendly Artificial Turf" />
          </div>
        </div>
      </section>
    </>
  );
};

export default PetFriendlyArtificialTurf;