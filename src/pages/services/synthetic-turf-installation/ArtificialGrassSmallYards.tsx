import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Gallery from '../../../components/ui/Gallery';
import ContactForm from '../../../components/forms/ContactForm';
import Button from '../../../components/ui/Button';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { services } from '../../../utils/constants';
import { landscapeImages } from '../../../utils/images';

const ArtificialGrassSmallYards: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Synthetic Turf Installation', url: 'https://americanelmlandscape.com/synthetic-turf-installation' },
    { name: 'Artificial Grass Small Yards Boston', url: 'https://americanelmlandscape.com/synthetic-turf-installation/artificial-grass-small-yards' }
  ];

  const benefits = [
    { title: 'No Mowing Required', description: 'Eliminate the hassle of mowing tiny yards with limited storage for equipment', icon: '✂️' },
    { title: 'Year-Round Green', description: 'Maintain a lush, green appearance even in shaded or high-traffic small spaces', icon: '🌿' },
    { title: 'Maximize Space', description: 'No need for lawn equipment storage, freeing up valuable space in small yards', icon: '📐' },
    { title: 'Low Maintenance', description: 'Perfect for busy urban homeowners who want beautiful yards without the work', icon: '⏰' }
  ];

  const idealFor = [
    'Small Back Bay courtyards',
    'Narrow South End gardens',
    'Shaded urban yards',
    'High-traffic areas',
    'Rooftop terraces',
    'Balcony spaces',
    'Pet areas in small yards',
    'Play areas for children'
  ];

  const galleryImages = [
    { src: landscapeImages.gallery.smallYardTurf, alt: 'Artificial grass small yard Boston', caption: 'Synthetic turf in compact Back Bay yard' },
    { src: landscapeImages.gallery.urbanTurf, alt: 'Urban artificial grass', caption: 'Perfect green space in South End' },
    { src: landscapeImages.gallery.courtyardTurf, alt: 'Courtyard synthetic turf', caption: 'Lush courtyard with artificial grass' },
    { src: landscapeImages.gallery.turfInstall, alt: 'Small yard turf installation', caption: 'Professional installation in tight space' }
  ];

  const relatedServices = services.syntheticTurf.filter(service => service.name !== 'Artificial Grass Small Yards').slice(0, 3);

  return (
    <>
      <SEO
        title="Artificial Grass Small Yards Boston | Perfect for Urban Spaces | American Elm Landscape"
        description="Artificial grass installation for small urban yards in Boston. Perfect solutions for tight spaces and challenging urban environments. Low maintenance, year-round green."
        keywords="artificial grass small yards Boston, synthetic turf urban spaces, small yard artificial grass, compact yard turf, urban synthetic grass"
        canonical="https://americanelmlandscape.com/synthetic-turf-installation/artificial-grass-small-yards"
        schema={[
          createServiceSchema('Artificial Grass Small Yards Boston', 'Professional artificial grass installation for small urban yards in Boston, providing low-maintenance green spaces in compact areas.', 'Boston'),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      <Hero title="Artificial Grass Small Yards Boston" subtitle="Perfect Artificial Grass Solutions for Urban Spaces" backgroundImage={landscapeImages.hero.artificialGrass} ctaText="Get Free Quote" ctaLink="/contact" height="medium" />

      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <Link to="/synthetic-turf-installation" className="text-dark-green hover:text-light-green">Synthetic Turf Installation</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Artificial Grass Small Yards Boston</span>
          </nav>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h1 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-4xl md:text-5xl font-serif font-bold text-dark-green mb-8">
              Artificial Grass Small Yards Boston
            </motion.h1>
            <motion.div initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: 0.2 }} className="prose prose-lg max-w-none text-gray-700 mb-12">
              <p className="text-xl leading-relaxed mb-6">
                Transform your small Boston yard into a beautiful, low-maintenance green space with professional artificial grass installation. Perfect for compact urban properties, our synthetic turf provides year-round beauty without the hassle of mowing, watering, or maintaining natural grass in tight spaces.
              </p>
              <h2 className="text-2xl font-serif font-semibold text-dark-green mb-4">Ideal Solution for Urban Small Yards</h2>
              <p className="leading-relaxed mb-6">
                Small urban yards present unique challenges—limited space for lawn equipment, shaded areas where grass won't grow, high foot traffic, and the desire for low-maintenance beauty. Artificial grass solves all these problems, providing a lush, green surface that looks great year-round without requiring mowing, fertilizing, or watering.
              </p>
              <p className="leading-relaxed mb-6">
                Our premium synthetic turf is specifically designed for Boston's climate, with excellent drainage to handle our heavy rains and snow, UV protection to prevent fading, and realistic appearance that enhances your small outdoor space. We handle complete installation including proper base preparation, drainage, and professional finishing for a flawless result.
              </p>
              <h3 className="text-xl font-semibold text-dark-green mb-4">Maximize Your Small Space</h3>
              <p className="leading-relaxed">
                With artificial grass, you eliminate the need for lawn mower storage, fertilizer, and other lawn care equipment—freeing up valuable space in your small yard. Enjoy a beautiful green space that's always ready for relaxation or entertaining, without the ongoing maintenance burden.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12">
            Benefits for Small Yards
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <motion.div key={index} initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="text-center bg-white p-6 rounded-lg shadow-lg">
                <div className="text-4xl mb-4">{benefit.icon}</div>
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">{benefit.title}</h3>
                <p className="text-gray-700">{benefit.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-8">
              Ideal Applications
            </motion.h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {idealFor.map((application, index) => (
                <motion.div key={index} initial={{ opacity: 0, x: -20 }} whileInView={{ opacity: 1, x: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="flex items-start">
                  <svg className="w-6 h-6 text-dark-green mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-lg text-gray-700">{application}</span>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12">
            Small Yard Turf Installations
          </motion.h2>
          <Gallery images={galleryImages} />
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl font-serif font-bold text-dark-green text-center mb-12">
            Related Synthetic Turf Services
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {relatedServices.map((service, index) => (
              <motion.div key={service.path} initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="bg-white rounded-lg shadow-lg p-6 text-center">
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">{service.name} Boston</h3>
                <p className="text-gray-700 mb-4">{service.description}</p>
                <Button to={service.path} variant="outline" size="sm">Learn More</Button>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6">Ready for Artificial Grass?</h2>
              <p className="text-lg text-gray-700">Contact us for a free consultation and quote for your small yard artificial grass installation.</p>
            </motion.div>
            <ContactForm prefilledService="Artificial Grass Small Yards" />
          </div>
        </div>
      </section>
    </>
  );
};

export default ArtificialGrassSmallYards;
