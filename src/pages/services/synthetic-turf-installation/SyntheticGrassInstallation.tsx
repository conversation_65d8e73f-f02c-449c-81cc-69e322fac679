import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Gallery from '../../../components/ui/Gallery';
import ContactForm from '../../../components/forms/ContactForm';
import Button from '../../../components/ui/Button';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { landscapeImages } from '../../../utils/images';

const SyntheticGrassInstallation: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Synthetic Turf Installation', url: 'https://americanelmlandscape.com/synthetic-turf-installation' },
    { name: 'Synthetic Grass Installation Boston', url: 'https://americanelmlandscape.com/services/synthetic-turf-installation/synthetic-grass-installation' }
  ];

  const processSteps = [
    {
      title: 'Site Evaluation & Measurement',
      description: 'Comprehensive assessment of your space including drainage, soil conditions, and usage requirements.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    },
    {
      title: 'Excavation & Base Preparation',
      description: 'Proper excavation and installation of drainage systems with compacted base materials for stability.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
        </svg>
      )
    },
    {
      title: 'Turf Installation & Seaming',
      description: 'Professional installation with invisible seams, proper stretching, and secure anchoring systems.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
        </svg>
      )
    },
    {
      title: 'Infill & Final Grooming',
      description: 'Application of infill materials and final grooming to achieve the perfect natural grass appearance.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
        </svg>
      )
    }
  ];

  const turfFeatures = [
    {
      title: 'Premium Materials',
      description: 'High-quality synthetic grass with UV protection, antimicrobial properties, and realistic appearance.',
      benefits: ['15-20 year lifespan', 'UV resistant fibers', 'Lead-free materials', 'Antimicrobial backing']
    },
    {
      title: 'Advanced Drainage',
      description: 'Superior drainage systems that handle Boston\'s heavy rainfall and prevent water accumulation.',
      benefits: ['Quick water drainage', 'Prevents flooding', 'Reduces mud and puddles', 'Year-round usability']
    },
    {
      title: 'Professional Installation',
      description: 'Expert installation techniques ensuring proper base preparation and long-lasting results.',
      benefits: ['Invisible seams', 'Proper anchoring', 'Level surfaces', 'Quality guarantee']
    }
  ];

  const galleryImages = [
    {
      src: landscapeImages.hero.syntheticTurf,
      alt: 'Synthetic grass installation Boston backyard',
      caption: 'Complete backyard transformation with premium artificial grass'
    },
    {
      src: landscapeImages.gallery.turf,
      alt: 'Professional synthetic turf installation process',
      caption: 'Professional installation with proper base preparation'
    },
    {
      src: landscapeImages.gallery.smallYard,
      alt: 'Small yard synthetic grass Boston',
      caption: 'Perfect solution for small urban yards'
    },
    {
      src: landscapeImages.gallery.puttingGreen,
      alt: 'Synthetic grass with landscape integration',
      caption: 'Seamless integration with existing landscape features'
    }
  ];

  return (
    <>
      <SEO
        title="Synthetic Grass Installation Boston | Professional Artificial Turf | American Elm Landscape"
        description="Professional synthetic grass installation in Boston. High-quality artificial turf for residential and commercial properties with expert installation and premium materials."
        keywords="synthetic grass installation Boston, artificial turf installation, professional turf installation, artificial grass Boston, synthetic lawn installation"
        canonical="https://americanelmlandscape.com/synthetic-turf-installation/synthetic-grass-installation"
        schema={[
          createServiceSchema(
            'Synthetic Grass Installation Boston',
            'Professional synthetic grass and artificial turf installation services for Boston area properties with premium materials and expert craftsmanship.'
          ),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      {/* Hero Section */}
      <Hero
        title="Synthetic Grass Installation Boston"
        subtitle="Professional Artificial Turf Installation Services"
        backgroundImage={landscapeImages.hero.syntheticTurf}
        ctaText="Get Free Quote"
        ctaLink="/contact"
        height="medium"
      />

      {/* Breadcrumbs */}
      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <Link to="/synthetic-turf-installation" className="text-dark-green hover:text-light-green">Synthetic Turf Installation</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Synthetic Grass Installation Boston</span>
          </nav>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6"
            >
              Professional Synthetic Grass Installation for Boston Properties
            </motion.h2>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="prose prose-lg max-w-none text-gray-700"
            >
              <p className="text-xl leading-relaxed mb-6">
                Transform your Boston property with professional synthetic grass installation that provides a
                beautiful, low-maintenance lawn year-round. Our premium artificial turf solutions are perfect
                for urban properties where natural grass struggles to thrive due to shade, poor soil, or heavy use.
              </p>

              <p className="text-lg leading-relaxed mb-6">
                From compact Back Bay courtyards to expansive Brookline estates, our synthetic grass installations
                are designed to withstand Boston's harsh climate while providing a consistently green, safe, and
                comfortable surface for your family and pets to enjoy throughout all four seasons.
              </p>

              <p className="text-lg leading-relaxed">
                Using only the highest quality materials and proven installation techniques, we create synthetic
                lawns that look and feel remarkably like natural grass while eliminating the need for watering,
                mowing, fertilizing, and pest control.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Our Professional Installation Process
          </motion.h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
            {processSteps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-dark-green rounded-full flex items-center justify-center mx-auto mb-4 text-white">
                  {step.icon}
                </div>
                <h3 className="text-xl font-serif font-bold text-dark-green mb-3">{step.title}</h3>
                <p className="text-gray-600">{step.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Premium Synthetic Grass Features
          </motion.h2>

          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {turfFeatures.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white p-6 rounded-lg shadow-lg"
              >
                <h3 className="text-2xl font-serif font-bold text-dark-green mb-4">{feature.title}</h3>
                <p className="text-gray-600 mb-6">{feature.description}</p>
                <ul className="space-y-2">
                  {feature.benefits.map((benefit, benefitIndex) => (
                    <li key={benefitIndex} className="flex items-center text-gray-700">
                      <svg className="w-5 h-5 text-dark-green mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      {benefit}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 bg-dark-green text-white">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-center mb-12"
          >
            Benefits of Professional Synthetic Grass Installation
          </motion.h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-dark-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold mb-3">Cost Savings</h3>
              <p className="text-gray-200">
                Eliminate ongoing costs for water, fertilizer, pest control, and lawn maintenance
                equipment while reducing your water bill significantly.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-dark-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold mb-3">Year-Round Beauty</h3>
              <p className="text-gray-200">
                Maintain a perfect green lawn through Boston's harsh winters, hot summers,
                and everything in between without seasonal dormancy or brown patches.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-dark-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold mb-3">Safe for Families & Pets</h3>
              <p className="text-gray-200">
                Non-toxic, lead-free materials with antimicrobial properties provide a safe
                playing surface for children and pets without harmful chemicals or allergens.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Our Synthetic Grass Installations
          </motion.h2>

          <Gallery images={galleryImages} />
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-8"
            >
              Ready for a Perfect Lawn Year-Round?
            </motion.h2>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-xl text-center mb-12 text-gray-700"
            >
              Contact us today for a free consultation and estimate for your synthetic grass installation project.
            </motion.p>

            <ContactForm />
          </div>
        </div>
      </section>

      {/* Local SEO Section */}
      <section className="py-8 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <p className="text-sm text-gray-600">
              <strong>American Elm Landscape</strong> - Professional synthetic grass and artificial turf installation
              serving Boston, Cambridge, Somerville, Brookline, Back Bay, South End, and surrounding areas. Licensed
              and insured contractors specializing in premium artificial grass systems for residential and commercial
              properties with expert installation and long-term warranties.
            </p>
          </div>
        </div>
      </section>
    </>
  );
};

export default SyntheticGrassInstallation;