import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { Trophy, Target, Clock } from 'lucide-react';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Gallery from '../../../components/ui/Gallery';
import ContactForm from '../../../components/forms/ContactForm';
import Button from '../../../components/ui/Button';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { services } from '../../../utils/constants';
import { landscapeImages } from '../../../utils/images';

const LuxuryHardscapeDesign: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Custom Hardscaping & Patios', url: 'https://americanelmlandscape.com/custom-hardscaping-patios' },
    { name: 'Luxury Hardscape Design Boston', url: 'https://americanelmlandscape.com/custom-hardscaping-patios/luxury-hardscape-design' }
  ];

  const luxuryFeatures = [
    { title: 'Premium Natural Stone', description: 'Imported and domestic stone including bluestone, granite, limestone, and travertine', icon: '💎' },
    { title: 'Custom Fabrication', description: 'Bespoke features crafted to your exact specifications and design vision', icon: '🎨' },
    { title: 'Integrated Lighting', description: 'Professional landscape lighting to showcase your hardscape day and night', icon: '💡' },
    { title: 'Water Features', description: 'Custom fountains, pools, and water elements integrated seamlessly', icon: '⛲' }
  ];

  const premiumMaterials = [
    'Imported natural stone',
    'Custom-cut bluestone',
    'Thermal bluestone',
    'Granite pavers and slabs',
    'Travertine and limestone',
    'Porcelain pavers',
    'Custom metalwork',
    'Premium sealers and finishes'
  ];

  const galleryImages = [
    { src: landscapeImages.gallery.luxuryPatio, alt: 'Luxury hardscape design Boston', caption: 'Premium bluestone patio in Brookline' },
    { src: landscapeImages.gallery.customHardscape, alt: 'Custom hardscape features', caption: 'Integrated water feature and seating' },
    { src: landscapeImages.gallery.elegantOutdoor, alt: 'Elegant outdoor space', caption: 'Sophisticated outdoor living area' },
    { src: landscapeImages.gallery.premiumStone, alt: 'Premium stone installation', caption: 'Custom natural stone installation' }
  ];

  const relatedServices = services.hardscaping.filter(service => service.name !== 'Luxury Hardscape Design').slice(0, 3);

  return (
    <>
      <SEO
        title="Luxury Hardscape Design Boston | Premium Materials & Expert Craftsmanship | American Elm Landscape"
        description="Luxury hardscape design and installation in Boston using premium natural stone, custom features, and expert craftsmanship for discerning homeowners."
        keywords="luxury hardscape design Boston, premium hardscaping, high-end outdoor design, natural stone patios, custom hardscaping, luxury outdoor living"
        canonical="https://americanelmlandscape.com/custom-hardscaping-patios/luxury-hardscape-design"
        schema={[
          createServiceSchema('Luxury Hardscape Design Boston', 'Premium hardscape design and installation services for discerning Boston homeowners using the finest materials and expert craftsmanship.', 'Boston'),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      <Hero title="Luxury Hardscape Design Boston" subtitle="Premium Materials & Expert Craftsmanship for Discerning Homeowners" backgroundImage={landscapeImages.hero.luxuryHardscape} ctaText="Schedule Consultation" ctaLink="/contact" height="medium" />

      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <Link to="/custom-hardscaping-patios" className="text-dark-green hover:text-light-green">Custom Hardscaping & Patios</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Luxury Hardscape Design Boston</span>
          </nav>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h1 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-4xl md:text-5xl font-serif font-bold text-dark-green mb-8">
              Luxury Hardscape Design Boston
            </motion.h1>
            <motion.div initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: 0.2 }} className="prose prose-lg max-w-none text-gray-700 mb-12">
              <p className="text-xl leading-relaxed mb-6">
                Elevate your Boston property with luxury hardscape design that combines premium materials, expert craftsmanship, and sophisticated design. We create bespoke outdoor spaces that reflect your refined taste and enhance your property's value and beauty.
              </p>
              <h2 className="text-2xl font-serif font-semibold text-dark-green mb-4">Uncompromising Quality for Discerning Homeowners</h2>
              <p className="leading-relaxed mb-6">
                Our luxury hardscape designs feature the finest natural stone, custom fabrication, and meticulous attention to detail. From imported bluestone to custom-cut granite, we source premium materials and employ master craftsmen to create outdoor spaces of exceptional quality and timeless elegance.
              </p>
              <p className="leading-relaxed mb-6">
                We understand that luxury is in the details—perfectly cut joints, seamless transitions, integrated lighting, and custom features that make your outdoor space truly unique. Our design-build approach ensures flawless execution from concept through completion, with dedicated project management and the highest standards of craftsmanship.
              </p>
              <h3 className="text-xl font-semibold text-dark-green mb-4">Investment in Lasting Beauty</h3>
              <p className="leading-relaxed">
                Luxury hardscaping is an investment in your property's beauty, functionality, and value. Our installations are built to last generations, using time-tested techniques and premium materials that age gracefully and withstand Boston's challenging climate.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12">
            Luxury Hardscape Features
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {luxuryFeatures.map((feature, index) => (
              <motion.div key={index} initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="text-center bg-white p-6 rounded-lg shadow-lg">
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">{feature.title}</h3>
                <p className="text-gray-700">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-8">
              Premium Materials We Use
            </motion.h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {premiumMaterials.map((material, index) => (
                <motion.div key={index} initial={{ opacity: 0, x: -20 }} whileInView={{ opacity: 1, x: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="flex items-start">
                  <svg className="w-6 h-6 text-dark-green mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-lg text-gray-700">{material}</span>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-dark-green text-white">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-center mb-12">
            The Luxury Difference
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <motion.div initial={{ opacity: 0, y: 20 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-center">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-white/10 rounded-full mb-4">
                <Trophy className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-xl font-serif font-bold mb-3">Master Craftsmanship</h3>
              <p className="text-gray-200">Our team includes master masons and craftsmen with decades of experience in luxury hardscaping.</p>
            </motion.div>
            <motion.div initial={{ opacity: 0, y: 20 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: 0.1 }} className="text-center">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-white/10 rounded-full mb-4">
                <Target className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-xl font-serif font-bold mb-3">Bespoke Design</h3>
              <p className="text-gray-200">Every project is custom-designed to your specifications, never cookie-cutter or template-based.</p>
            </motion.div>
            <motion.div initial={{ opacity: 0, y: 20 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: 0.2 }} className="text-center">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-white/10 rounded-full mb-4">
                <Clock className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-xl font-serif font-bold mb-3">Timeless Quality</h3>
              <p className="text-gray-200">Built to last generations using premium materials and time-tested construction techniques.</p>
            </motion.div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12">
            Luxury Hardscape Portfolio
          </motion.h2>
          <Gallery images={galleryImages} />
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl font-serif font-bold text-dark-green text-center mb-12">
            Related Hardscaping Services
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {relatedServices.map((service, index) => (
              <motion.div key={service.path} initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="bg-white rounded-lg shadow-lg p-6 text-center">
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">{service.name} Boston</h3>
                <p className="text-gray-700 mb-4">{service.description}</p>
                <Button to={service.path} variant="outline" size="sm">Learn More</Button>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6">Ready for Luxury Hardscaping?</h2>
              <p className="text-lg text-gray-700">Schedule a consultation to discuss your vision for a luxury outdoor space. We'll create a bespoke design that exceeds your expectations.</p>
            </motion.div>
            <ContactForm prefilledService="Luxury Hardscape Design" />
          </div>
        </div>
      </section>
    </>
  );
};

export default LuxuryHardscapeDesign;
