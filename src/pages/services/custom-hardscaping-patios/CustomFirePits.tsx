import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Gallery from '../../../components/ui/Gallery';
import ContactForm from '../../../components/forms/ContactForm';
import Button from '../../../components/ui/Button';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { services } from '../../../utils/constants';
import { landscapeImages } from '../../../utils/images';

const CustomFirePits: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Custom Hardscaping & Patios', url: 'https://americanelmlandscape.com/custom-hardscaping-patios' },
    { name: 'Custom Fire Pits Boston', url: 'https://americanelmlandscape.com/custom-hardscaping-patios/custom-fire-pits' }
  ];

  const firePitStyles = [
    {
      title: 'Natural Stone Fire Pits',
      description: 'Handcrafted using bluestone, granite, or fieldstone for timeless elegance and durability.',
      features: ['Unique natural appearance', 'Weather resistant', 'Custom shapes and sizes', 'Integrates with landscape']
    },
    {
      title: 'Modern Steel Fire Pits',
      description: 'Contemporary designs using Corten steel or stainless steel for sleek, modern aesthetics.',
      features: ['Clean modern lines', 'Rust-resistant options', 'Custom fabrication', 'Low maintenance']
    },
    {
      title: 'Built-In Fire Features',
      description: 'Integrated fire pits built into patios, seating walls, or outdoor living spaces.',
      features: ['Seamless integration', 'Space efficient', 'Multiple fuel options', 'Professional installation']
    }
  ];

  const fuelOptions = [
    { type: 'Natural Gas', benefits: ['Clean burning', 'Easy on/off control', 'No ash or cleanup', 'Consistent flame'] },
    { type: 'Propane', benefits: ['Portable options', 'No gas line needed', 'Adjustable flame', 'Reliable performance'] },
    { type: 'Wood Burning', benefits: ['Traditional ambiance', 'Crackling sounds', 'Natural aroma', 'Higher heat output'] }
  ];

  const galleryImages = [
    {
      src: landscapeImages.gallery.firepit,
      alt: 'Custom fire pit Boston',
      caption: 'Natural stone fire pit in Back Bay'
    },
    {
      src: landscapeImages.gallery.firePitModern,
      alt: 'Modern steel fire pit',
      caption: 'Contemporary Corten steel fire feature'
    },
    {
      src: landscapeImages.gallery.firePitSeating,
      alt: 'Fire pit with seating',
      caption: 'Integrated fire pit with seating wall'
    },
    {
      src: landscapeImages.gallery.firePitEvening,
      alt: 'Fire pit evening ambiance',
      caption: 'Evening gathering around custom fire pit'
    }
  ];

  const relatedServices = services.hardscaping.filter(service =>
    service.name !== 'Custom Fire Pits'
  ).slice(0, 3);

  return (
    <>
      <SEO
        title="Custom Fire Pits Boston | Bespoke Outdoor Fire Features | American Elm Landscape"
        description="Custom fire pit design and installation in Boston. Bespoke fire features using natural stone, steel, and premium materials for year-round outdoor enjoyment."
        keywords="custom fire pits Boston, fire pit installation, outdoor fire features, stone fire pits, backyard fire pits, gas fire pits"
        canonical="https://americanelmlandscape.com/custom-hardscaping-patios/custom-fire-pits"
        schema={[
          createServiceSchema(
            'Custom Fire Pits Boston',
            'Custom fire pit design and installation services for Boston properties, creating beautiful outdoor gathering spaces.',
            'Boston'
          ),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      {/* Hero Section */}
      <Hero
        title="Custom Fire Pits Boston"
        subtitle="Bespoke Fire Features for Year-Round Outdoor Enjoyment"
        backgroundImage={landscapeImages.hero.firepit}
        ctaText="Design Your Fire Pit"
        ctaLink="/contact"
        height="medium"
      />

      {/* Breadcrumbs */}
      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <Link to="/custom-hardscaping-patios" className="text-dark-green hover:text-light-green">Custom Hardscaping & Patios</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Custom Fire Pits Boston</span>
          </nav>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-serif font-bold text-dark-green mb-8"
            >
              Custom Fire Pits Boston
            </motion.h1>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="prose prose-lg max-w-none text-gray-700 mb-12"
            >
              <p className="text-xl leading-relaxed mb-6">
                Create the perfect gathering spot with a custom fire pit designed specifically for your Boston outdoor space.
                Our bespoke fire features combine beautiful design with expert craftsmanship, providing warmth, ambiance,
                and a focal point for outdoor entertaining throughout the year.
              </p>

              <h2 className="text-2xl font-serif font-semibold text-dark-green mb-4">
                Handcrafted Fire Features for Boston Properties
              </h2>

              <p className="leading-relaxed mb-6">
                Whether you envision a rustic stone fire pit, a sleek modern steel feature, or an integrated fire element
                within your patio, we create custom designs that complement your home's architecture and landscape style.
                Our fire pits are built to withstand Boston's weather while providing safe, reliable performance for years to come.
              </p>

              <p className="leading-relaxed mb-6">
                We handle every aspect of your fire pit project—from initial design and permitting to professional installation
                and gas line connections. Our team ensures your fire feature meets all local codes and safety requirements while
                creating a stunning centerpiece for your outdoor living space.
              </p>

              <h3 className="text-xl font-semibold text-dark-green mb-4">
                Extend Your Outdoor Season
              </h3>

              <p className="leading-relaxed">
                A custom fire pit extends your outdoor living season well into Boston's cooler months. Gather with family
                and friends around the warmth of a beautiful fire feature, creating memories and enjoying your outdoor space
                even on chilly New England evenings.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Fire Pit Styles Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Custom Fire Pit Styles
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {firePitStyles.map((style, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white p-6 rounded-lg shadow-lg"
              >
                <h3 className="text-2xl font-serif font-bold text-dark-green mb-4">{style.title}</h3>
                <p className="text-gray-600 mb-6">{style.description}</p>
                <ul className="space-y-2">
                  {style.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-gray-700">
                      <svg className="w-5 h-5 text-dark-green mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      {feature}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Fuel Options Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Fuel Options
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {fuelOptions.map((option, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-gray-50 p-6 rounded-lg"
              >
                <h3 className="text-2xl font-serif font-bold text-dark-green mb-4">{option.type}</h3>
                <ul className="space-y-2">
                  {option.benefits.map((benefit, benefitIndex) => (
                    <li key={benefitIndex} className="flex items-start text-gray-700">
                      <svg className="w-5 h-5 text-dark-green mt-1 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span>{benefit}</span>
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Custom Fire Pit Portfolio
          </motion.h2>

          <Gallery images={galleryImages} />
        </div>
      </section>

      {/* Related Services */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Related Hardscaping Services
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {relatedServices.map((service, index) => (
              <motion.div
                key={service.path}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-lg shadow-lg p-6 text-center"
              >
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">
                  {service.name} Boston
                </h3>
                <p className="text-gray-700 mb-4">{service.description}</p>
                <Button to={service.path} variant="outline" size="sm">
                  Learn More
                </Button>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6">
                Ready for Your Custom Fire Pit?
              </h2>
              <p className="text-lg text-gray-700">
                Let's design the perfect fire feature for your outdoor space. Contact us for a free consultation
                and bring warmth and ambiance to your Boston property.
              </p>
            </motion.div>

            <ContactForm prefilledService="Custom Fire Pits" />
          </div>
        </div>
      </section>
    </>
  );
};

export default CustomFirePits;
