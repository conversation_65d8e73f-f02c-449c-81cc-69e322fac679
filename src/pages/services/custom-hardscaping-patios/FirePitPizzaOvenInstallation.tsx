import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Gallery from '../../../components/ui/Gallery';
import ContactForm from '../../../components/forms/ContactForm';
import Button from '../../../components/ui/Button';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { services } from '../../../utils/constants';
import { landscapeImages } from '../../../utils/images';

const FirePitPizzaOvenInstallation: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Custom Hardscaping & Patios', url: 'https://americanelmlandscape.com/custom-hardscaping-patios' },
    { name: 'Fire Pit & Pizza Oven Installation Boston', url: 'https://americanelmlandscape.com/custom-hardscaping-patios/fire-pit-pizza-oven-installation' }
  ];

  const features = [
    {
      title: 'Custom Pizza Ovens',
      description: 'Authentic wood-fired or gas pizza ovens built with premium materials for restaurant-quality results at home.',
      icon: '🍕'
    },
    {
      title: 'Integrated Fire Pits',
      description: 'Beautifully designed fire pits that complement your pizza oven and create a complete outdoor cooking experience.',
      icon: '🔥'
    },
    {
      title: 'Outdoor Kitchens',
      description: 'Complete outdoor kitchen integration with countertops, storage, and prep areas surrounding your fire features.',
      icon: '👨‍🍳'
    },
    {
      title: 'Year-Round Use',
      description: 'Designed for Boston weather with proper insulation and materials that withstand freeze-thaw cycles.',
      icon: '🌟'
    }
  ];

  const benefits = [
    'Authentic wood-fired cooking experience',
    'Increases property value significantly',
    'Creates ultimate entertainment space',
    'Professional-grade cooking results',
    'Custom design to match your style',
    'Durable construction for decades of use',
    'Gas or wood-fired options',
    'Complete outdoor kitchen integration'
  ];

  const galleryImages = [
    {
      src: landscapeImages.gallery.pizzaOven,
      alt: 'Custom pizza oven installation Boston',
      caption: 'Wood-fired pizza oven in Brookline'
    },
    {
      src: landscapeImages.gallery.firepit,
      alt: 'Fire pit and pizza oven combination',
      caption: 'Integrated fire pit and pizza oven'
    },
    {
      src: landscapeImages.gallery.outdoorKitchen,
      alt: 'Outdoor kitchen with pizza oven',
      caption: 'Complete outdoor kitchen with pizza oven'
    },
    {
      src: landscapeImages.gallery.entertainment,
      alt: 'Outdoor entertainment area',
      caption: 'Ultimate outdoor entertainment space'
    }
  ];

  const relatedServices = services.hardscaping.filter(service =>
    service.name !== 'Fire Pit & Pizza Oven Installation'
  ).slice(0, 3);

  return (
    <>
      <SEO
        title="Fire Pit & Pizza Oven Installation Boston | Custom Outdoor Cooking | American Elm Landscape"
        description="Custom fire pit and pizza oven installation in Boston. Create the ultimate outdoor cooking and entertainment space with professional design and installation."
        keywords="pizza oven installation Boston, fire pit installation, outdoor pizza oven, wood-fired oven, outdoor kitchen, fire pit and pizza oven"
        canonical="https://americanelmlandscape.com/custom-hardscaping-patios/fire-pit-pizza-oven-installation"
        schema={[
          createServiceSchema(
            'Fire Pit & Pizza Oven Installation Boston',
            'Professional fire pit and pizza oven installation services for Boston properties, creating ultimate outdoor cooking and entertainment spaces.',
            'Boston'
          ),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      {/* Hero Section */}
      <Hero
        title="Fire Pit & Pizza Oven Installation Boston"
        subtitle="Create the Ultimate Outdoor Cooking & Entertainment Space"
        backgroundImage={landscapeImages.hero.pizzaOven}
        ctaText="Design Your Outdoor Kitchen"
        ctaLink="/contact"
        height="medium"
      />

      {/* Breadcrumbs */}
      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <Link to="/custom-hardscaping-patios" className="text-dark-green hover:text-light-green">Custom Hardscaping & Patios</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Fire Pit & Pizza Oven Installation Boston</span>
          </nav>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-serif font-bold text-dark-green mb-8"
            >
              Fire Pit & Pizza Oven Installation Boston
            </motion.h1>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="prose prose-lg max-w-none text-gray-700 mb-12"
            >
              <p className="text-xl leading-relaxed mb-6">
                Transform your Boston backyard into the ultimate outdoor cooking and entertainment destination with custom
                fire pit and pizza oven installation. Our expert team designs and builds authentic outdoor cooking features
                that bring restaurant-quality results to your home while creating unforgettable gathering spaces.
              </p>

              <h2 className="text-2xl font-serif font-semibold text-dark-green mb-4">
                Authentic Outdoor Cooking Experiences
              </h2>

              <p className="leading-relaxed mb-6">
                There's nothing quite like the taste of wood-fired pizza or the ambiance of gathering around a custom fire
                pit. We specialize in creating integrated outdoor cooking spaces that combine beautiful design with functional
                excellence. From traditional brick pizza ovens to modern gas-fired units, we build features that match your
                style and cooking preferences.
              </p>

              <p className="leading-relaxed mb-6">
                Our installations are designed specifically for Boston's climate, using materials and construction techniques
                that withstand freeze-thaw cycles and provide years of reliable performance. We handle all aspects of the
                project—design, permitting, gas line installation, and professional construction—ensuring a seamless process
                from concept to completion.
              </p>

              <h3 className="text-xl font-semibold text-dark-green mb-4">
                Complete Outdoor Kitchen Integration
              </h3>

              <p className="leading-relaxed">
                We don't just install standalone features—we create complete outdoor cooking environments. Our designs
                integrate pizza ovens and fire pits with countertops, storage, seating areas, and lighting to create
                functional, beautiful spaces that become the heart of your outdoor entertaining.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            What We Offer
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center bg-white p-6 rounded-lg shadow-lg"
              >
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">{feature.title}</h3>
                <p className="text-gray-700">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-8"
            >
              Benefits of Custom Fire Pit & Pizza Oven Installation
            </motion.h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {benefits.map((benefit, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="flex items-start"
                >
                  <svg className="w-6 h-6 text-dark-green mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-lg text-gray-700">{benefit}</span>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Our Outdoor Cooking Installations
          </motion.h2>

          <Gallery images={galleryImages} />
        </div>
      </section>

      {/* Related Services */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Related Hardscaping Services
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {relatedServices.map((service, index) => (
              <motion.div
                key={service.path}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-lg shadow-lg p-6 text-center"
              >
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">
                  {service.name} Boston
                </h3>
                <p className="text-gray-700 mb-4">{service.description}</p>
                <Button to={service.path} variant="outline" size="sm">
                  Learn More
                </Button>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6">
                Ready to Create Your Outdoor Cooking Space?
              </h2>
              <p className="text-lg text-gray-700">
                Let's design the perfect fire pit and pizza oven combination for your Boston property.
                Contact us for a free consultation.
              </p>
            </motion.div>

            <ContactForm prefilledService="Fire Pit & Pizza Oven Installation" />
          </div>
        </div>
      </section>
    </>
  );
};

export default FirePitPizzaOvenInstallation;
