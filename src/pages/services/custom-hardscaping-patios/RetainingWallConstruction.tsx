import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Gallery from '../../../components/ui/Gallery';
import ContactForm from '../../../components/forms/ContactForm';
import Button from '../../../components/ui/Button';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { services } from '../../../utils/constants';
import { landscapeImages } from '../../../utils/images';

const RetainingWallConstruction: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Custom Hardscaping & Patios', url: 'https://americanelmlandscape.com/custom-hardscaping-patios' },
    { name: 'Retaining Wall Construction Boston', url: 'https://americanelmlandscape.com/custom-hardscaping-patios/retaining-wall-construction' }
  ];

  const wallTypes = [
    {
      title: 'Natural Stone Walls',
      description: 'Timeless beauty using fieldstone, granite, or bluestone for elegant, durable retaining walls.',
      features: ['Unique natural appearance', 'Exceptional durability', 'Increases property value', 'Weather resistant']
    },
    {
      title: 'Segmental Block Walls',
      description: 'Engineered retaining wall systems offering strength, versatility, and design flexibility.',
      features: ['Cost-effective solution', 'Wide design options', 'Easy maintenance', 'Proven engineering']
    },
    {
      title: 'Timber Walls',
      description: 'Natural wood retaining walls perfect for rustic or traditional landscape designs.',
      features: ['Natural aesthetic', 'Budget-friendly', 'Quick installation', 'Versatile applications']
    }
  ];

  const constructionProcess = [
    {
      step: '1',
      title: 'Site Analysis',
      description: 'Comprehensive evaluation of soil conditions, drainage, slope, and structural requirements.'
    },
    {
      step: '2',
      title: 'Engineering & Design',
      description: 'Professional design with proper engineering to ensure long-term stability and code compliance.'
    },
    {
      step: '3',
      title: 'Excavation & Base',
      description: 'Precise excavation and compacted base preparation for maximum wall stability.'
    },
    {
      step: '4',
      title: 'Wall Construction',
      description: 'Expert installation with proper drainage, reinforcement, and finishing details.'
    }
  ];

  const galleryImages = [
    {
      src: landscapeImages.gallery.retainingWall,
      alt: 'Retaining wall construction Boston',
      caption: 'Natural stone retaining wall in Brookline'
    },
    {
      src: landscapeImages.gallery.hardscape,
      alt: 'Segmental block retaining wall',
      caption: 'Modern segmental block wall in Cambridge'
    },
    {
      src: landscapeImages.gallery.terraced,
      alt: 'Terraced retaining walls Boston',
      caption: 'Multi-level terraced walls in Back Bay'
    },
    {
      src: landscapeImages.gallery.wall,
      alt: 'Retaining wall with plantings',
      caption: 'Integrated plantings with retaining wall'
    }
  ];

  const relatedServices = services.hardscaping.filter(service =>
    service.name !== 'Retaining Wall Construction'
  ).slice(0, 3);

  return (
    <>
      <SEO
        title="Retaining Wall Construction Boston | Expert Installation | American Elm Landscape"
        description="Professional retaining wall construction in Boston. Expert design and installation using natural stone, segmental blocks, and timber. Solve erosion and create usable space."
        keywords="retaining wall construction Boston, retaining wall installation, stone retaining walls, segmental block walls, erosion control, terraced walls"
        canonical="https://americanelmlandscape.com/custom-hardscaping-patios/retaining-wall-construction"
        schema={[
          createServiceSchema(
            'Retaining Wall Construction Boston',
            'Professional retaining wall construction services for Boston properties, providing erosion control and creating usable outdoor space.',
            'Boston'
          ),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      {/* Hero Section */}
      <Hero
        title="Retaining Wall Construction Boston"
        subtitle="Expert Design & Installation for Slope Control and Usable Space"
        backgroundImage={landscapeImages.hero.retainingWall}
        ctaText="Get Free Estimate"
        ctaLink="/contact"
        height="medium"
      />

      {/* Breadcrumbs */}
      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <Link to="/custom-hardscaping-patios" className="text-dark-green hover:text-light-green">Custom Hardscaping & Patios</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Retaining Wall Construction Boston</span>
          </nav>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-serif font-bold text-dark-green mb-8"
            >
              Retaining Wall Construction Boston
            </motion.h1>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="prose prose-lg max-w-none text-gray-700 mb-12"
            >
              <p className="text-xl leading-relaxed mb-6">
                Transform sloped or uneven terrain into functional, beautiful outdoor space with professional retaining
                wall construction. Our expert team designs and builds structurally sound retaining walls that control
                erosion, create level areas, and enhance your Boston property's aesthetics and value.
              </p>

              <h2 className="text-2xl font-serif font-semibold text-dark-green mb-4">
                Expert Retaining Wall Solutions for Boston Properties
              </h2>

              <p className="leading-relaxed mb-6">
                Boston's hilly terrain and urban lots often require retaining walls to manage slopes, prevent erosion,
                and maximize usable space. We specialize in designing and constructing retaining walls that are both
                structurally sound and visually appealing, using materials that complement your home and landscape.
              </p>

              <p className="leading-relaxed mb-6">
                Our retaining walls are engineered to withstand Boston's freeze-thaw cycles, heavy rains, and soil
                pressures. We handle all aspects of construction—from initial site analysis and engineering to excavation,
                drainage installation, and final construction—ensuring your wall performs beautifully for decades.
              </p>

              <h3 className="text-xl font-semibold text-dark-green mb-4">
                Solving Slope and Drainage Challenges
              </h3>

              <p className="leading-relaxed">
                Retaining walls do more than hold back soil—they solve drainage problems, create terraced gardens,
                expand patio areas, and add architectural interest to your landscape. Our designs incorporate proper
                drainage systems, appropriate materials for your site conditions, and construction techniques that
                ensure long-term stability and performance.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Wall Types Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Retaining Wall Options
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {wallTypes.map((type, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white p-6 rounded-lg shadow-lg"
              >
                <h3 className="text-2xl font-serif font-bold text-dark-green mb-4">{type.title}</h3>
                <p className="text-gray-600 mb-6">{type.description}</p>
                <ul className="space-y-2">
                  {type.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-gray-700">
                      <svg className="w-5 h-5 text-dark-green mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      {feature}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Construction Process Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Our Construction Process
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
            {constructionProcess.map((process, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-dark-green rounded-full flex items-center justify-center mx-auto mb-4 text-white text-2xl font-bold">
                  {process.step}
                </div>
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">{process.title}</h3>
                <p className="text-gray-700">{process.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Retaining Wall Portfolio
          </motion.h2>

          <Gallery images={galleryImages} />
        </div>
      </section>

      {/* Related Services */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Related Hardscaping Services
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {relatedServices.map((service, index) => (
              <motion.div
                key={service.path}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-lg shadow-lg p-6 text-center"
              >
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">
                  {service.name} Boston
                </h3>
                <p className="text-gray-700 mb-4">{service.description}</p>
                <Button to={service.path} variant="outline" size="sm">
                  Learn More
                </Button>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6">
                Ready for Your Retaining Wall Project?
              </h2>
              <p className="text-lg text-gray-700">
                Contact us for a free consultation and estimate. We'll assess your site and recommend the best
                retaining wall solution for your needs.
              </p>
            </motion.div>

            <ContactForm prefilledService="Retaining Wall Construction" />
          </div>
        </div>
      </section>
    </>
  );
};

export default RetainingWallConstruction;
