import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Gallery from '../../../components/ui/Gallery';
import ContactForm from '../../../components/forms/ContactForm';
import Button from '../../../components/ui/Button';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { services } from '../../../utils/constants';
import { landscapeImages } from '../../../utils/images';

const WalkwayPathwayInstallation: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Custom Hardscaping & Patios', url: 'https://americanelmlandscape.com/custom-hardscaping-patios' },
    { name: 'Walkway & Pathway Installation Boston', url: 'https://americanelmlandscape.com/custom-hardscaping-patios/walkway-pathway-installation' }
  ];

  const pathwayMaterials = [
    { name: 'Natural Stone', description: 'Bluestone, granite, or flagstone for elegant, durable pathways', benefits: ['Timeless beauty', 'Slip-resistant', 'Weather resistant', 'Unique patterns'] },
    { name: 'Pavers', description: 'Concrete or clay pavers in various colors and patterns', benefits: ['Design flexibility', 'Easy repairs', 'Cost-effective', 'Wide variety'] },
    { name: 'Gravel & Stone', description: 'Crushed stone or decorative gravel for informal pathways', benefits: ['Excellent drainage', 'Budget-friendly', 'Easy installation', 'Natural look'] }
  ];

  const designFeatures = [
    'Proper grading and drainage',
    'Stable base preparation',
    'Edging and borders',
    'Lighting integration',
    'ADA-compliant options',
    'Curved or straight designs',
    'Multiple material combinations',
    'Low-maintenance solutions'
  ];

  const galleryImages = [
    { src: landscapeImages.gallery.walkway, alt: 'Custom walkway installation Boston', caption: 'Natural stone walkway in Cambridge' },
    { src: landscapeImages.gallery.pathway, alt: 'Paver pathway Boston', caption: 'Paver pathway with lighting' },
    { src: landscapeImages.gallery.gardenPath, alt: 'Garden pathway', caption: 'Curved garden pathway' },
    { src: landscapeImages.gallery.entrance, alt: 'Front entrance walkway', caption: 'Elegant front entrance walkway' }
  ];

  const relatedServices = services.hardscaping.filter(service => service.name !== 'Walkway & Pathway Installation').slice(0, 3);

  return (
    <>
      <SEO
        title="Walkway & Pathway Installation Boston | Custom Design & Build | American Elm Landscape"
        description="Professional walkway and pathway installation in Boston. Custom design using natural stone, pavers, and premium materials. Expert installation for beautiful, durable paths."
        keywords="walkway installation Boston, pathway installation, stone walkways, paver pathways, garden paths, front walkway design"
        canonical="https://americanelmlandscape.com/custom-hardscaping-patios/walkway-pathway-installation"
        schema={[
          createServiceSchema('Walkway & Pathway Installation Boston', 'Professional walkway and pathway installation services for Boston properties using premium materials and expert craftsmanship.', 'Boston'),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      <Hero title="Walkway & Pathway Installation Boston" subtitle="Custom Design & Expert Installation for Beautiful, Functional Paths" backgroundImage={landscapeImages.hero.walkway} ctaText="Get Free Estimate" ctaLink="/contact" height="medium" />

      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <Link to="/custom-hardscaping-patios" className="text-dark-green hover:text-light-green">Custom Hardscaping & Patios</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Walkway & Pathway Installation Boston</span>
          </nav>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h1 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-4xl md:text-5xl font-serif font-bold text-dark-green mb-8">
              Walkway & Pathway Installation Boston
            </motion.h1>
            <motion.div initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: 0.2 }} className="prose prose-lg max-w-none text-gray-700 mb-12">
              <p className="text-xl leading-relaxed mb-6">
                Create beautiful, functional connections throughout your Boston property with custom walkway and pathway installation. Our expert team designs and builds durable, attractive pathways that enhance your landscape's accessibility, safety, and aesthetic appeal.
              </p>
              <h2 className="text-2xl font-serif font-semibold text-dark-green mb-4">Expert Pathway Design for Boston Properties</h2>
              <p className="leading-relaxed mb-6">
                Well-designed walkways do more than provide access—they guide visitors, create visual interest, and tie your landscape together. We specialize in creating pathways that complement your home's architecture and landscape style while providing safe, stable surfaces that withstand Boston's challenging weather.
              </p>
              <p className="leading-relaxed">
                From formal front entrance walkways to casual garden paths, we handle every aspect of installation—design, excavation, base preparation, material installation, and finishing details. Our pathways are built to last, with proper drainage and stable foundations that prevent settling and cracking.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12">
            Pathway Material Options
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {pathwayMaterials.map((material, index) => (
              <motion.div key={index} initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="bg-white p-6 rounded-lg shadow-lg">
                <h3 className="text-2xl font-serif font-bold text-dark-green mb-4">{material.name}</h3>
                <p className="text-gray-600 mb-6">{material.description}</p>
                <ul className="space-y-2">
                  {material.benefits.map((benefit, benefitIndex) => (
                    <li key={benefitIndex} className="flex items-center text-gray-700">
                      <svg className="w-5 h-5 text-dark-green mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      {benefit}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-8">
              Design Features We Include
            </motion.h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {designFeatures.map((feature, index) => (
                <motion.div key={index} initial={{ opacity: 0, x: -20 }} whileInView={{ opacity: 1, x: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="flex items-start">
                  <svg className="w-6 h-6 text-dark-green mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-lg text-gray-700">{feature}</span>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12">
            Walkway & Pathway Portfolio
          </motion.h2>
          <Gallery images={galleryImages} />
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl font-serif font-bold text-dark-green text-center mb-12">
            Related Hardscaping Services
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {relatedServices.map((service, index) => (
              <motion.div key={service.path} initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="bg-white rounded-lg shadow-lg p-6 text-center">
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">{service.name} Boston</h3>
                <p className="text-gray-700 mb-4">{service.description}</p>
                <Button to={service.path} variant="outline" size="sm">Learn More</Button>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6">Ready for Your Custom Walkway?</h2>
              <p className="text-lg text-gray-700">Contact us for a free consultation and estimate for your walkway or pathway project.</p>
            </motion.div>
            <ContactForm prefilledService="Walkway & Pathway Installation" />
          </div>
        </div>
      </section>
    </>
  );
};

export default WalkwayPathwayInstallation;
