import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Gallery from '../../../components/ui/Gallery';
import ContactForm from '../../../components/forms/ContactForm';
import Button from '../../../components/ui/Button';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { landscapeImages } from '../../../utils/images';

const CustomPatioInstallation: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Custom Hardscaping & Patios', url: 'https://americanelmlandscape.com/custom-hardscaping-patios' },
    { name: 'Custom Patio Installation Boston', url: 'https://americanelmlandscape.com/services/custom-hardscaping-patios/custom-patio-installation' }
  ];

  const processSteps = [
    {
      title: 'Site Assessment & Design Consultation',
      description: 'We evaluate your space, soil conditions, and drainage requirements while discussing your vision for the perfect patio.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    },
    {
      title: 'Material Selection & Planning',
      description: 'Choose from premium natural stone, pavers, or concrete options that complement your home and withstand Boston\'s climate.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
        </svg>
      )
    },
    {
      title: 'Excavation & Base Preparation',
      description: 'Proper excavation and base preparation with drainage systems to ensure your patio remains stable and beautiful for decades.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
        </svg>
      )
    },
    {
      title: 'Professional Installation',
      description: 'Expert installation with precise leveling, proper joint spacing, and finishing touches that create a stunning outdoor space.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
        </svg>
      )
    }
  ];

  const patioMaterials = [
    {
      name: 'Natural Stone',
      description: 'Bluestone, granite, and limestone options that provide timeless elegance and durability.',
      benefits: ['Weather resistant', 'Unique natural patterns', 'Increases property value', 'Long-lasting beauty']
    },
    {
      name: 'Premium Pavers',
      description: 'High-quality concrete and clay pavers in various colors, textures, and patterns.',
      benefits: ['Easy maintenance', 'Slip-resistant surfaces', 'Design flexibility', 'Cost-effective']
    },
    {
      name: 'Stamped Concrete',
      description: 'Decorative concrete that mimics natural stone, brick, or tile at a fraction of the cost.',
      benefits: ['Seamless surface', 'Custom patterns', 'Low maintenance', 'Quick installation']
    }
  ];

  const galleryImages = [
    {
      src: landscapeImages.hero.hardscaping,
      alt: 'Custom patio installation Boston with natural stone',
      caption: 'Natural stone patio with integrated seating wall'
    },
    {
      src: landscapeImages.gallery.patio,
      alt: 'Premium paver patio installation Boston',
      caption: 'Multi-level paver patio with fire pit area'
    },
    {
      src: landscapeImages.gallery.hardscape,
      alt: 'Stamped concrete patio Boston',
      caption: 'Stamped concrete patio with decorative borders'
    },
    {
      src: landscapeImages.gallery.firepit,
      alt: 'Custom patio with outdoor kitchen Boston',
      caption: 'Complete outdoor living space with kitchen integration'
    }
  ];

  return (
    <>
      <SEO
        title="Custom Patio Installation Boston | Professional Hardscaping | American Elm Landscape"
        description="Expert custom patio installation in Boston using premium materials. Professional hardscaping services for beautiful, durable outdoor spaces that enhance your property value."
        keywords="custom patio installation Boston, patio contractors, hardscaping Boston, outdoor patio design, natural stone patio, paver patio installation"
        canonical="https://americanelmlandscape.com/custom-hardscaping-patios/custom-patio-installation"
        schema={[
          createServiceSchema(
            'Custom Patio Installation Boston',
            'Professional custom patio installation services using premium materials including natural stone, pavers, and decorative concrete for Boston area properties.'
          ),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      {/* Hero Section */}
      <Hero
        title="Custom Patio Installation Boston"
        subtitle="Professional Installation with Premium Materials and Expert Craftsmanship"
        backgroundImage={landscapeImages.hero.hardscaping}
        ctaText="Get Free Estimate"
        ctaLink="/contact"
        height="medium"
      />

      {/* Breadcrumbs */}
      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <Link to="/custom-hardscaping-patios" className="text-dark-green hover:text-light-green">Custom Hardscaping & Patios</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Custom Patio Installation Boston</span>
          </nav>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6"
            >
              Transform Your Outdoor Space with Custom Patio Installation
            </motion.h2>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="prose prose-lg max-w-none text-gray-700"
            >
              <p className="text-xl leading-relaxed mb-6">
                Create the perfect outdoor entertaining and relaxation space with professional custom patio installation.
                Our expert team specializes in designing and building beautiful, durable patios that enhance your Boston
                property's value and provide years of enjoyment for your family and guests.
              </p>

              <p className="text-lg leading-relaxed mb-6">
                From intimate Back Bay courtyards to expansive Brookline estates, we understand the unique challenges
                of Boston's urban environment and climate. Our patio installations are designed to withstand harsh
                New England winters while providing stunning outdoor spaces that complement your home's architecture.
              </p>

              <p className="text-lg leading-relaxed">
                Using only premium materials and proven installation techniques, we create custom patios that combine
                beauty, functionality, and longevity. Whether you prefer the natural elegance of stone, the versatility
                of pavers, or the seamless look of decorative concrete, we'll help you choose the perfect solution for
                your space and budget.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Our Custom Patio Installation Process
          </motion.h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
            {processSteps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-dark-green rounded-full flex items-center justify-center mx-auto mb-4 text-white">
                  {step.icon}
                </div>
                <h3 className="text-xl font-serif font-bold text-dark-green mb-3">{step.title}</h3>
                <p className="text-gray-600">{step.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Materials Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Premium Patio Materials
          </motion.h2>

          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {patioMaterials.map((material, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white p-6 rounded-lg shadow-lg"
              >
                <h3 className="text-2xl font-serif font-bold text-dark-green mb-4">{material.name}</h3>
                <p className="text-gray-600 mb-6">{material.description}</p>
                <ul className="space-y-2">
                  {material.benefits.map((benefit, benefitIndex) => (
                    <li key={benefitIndex} className="flex items-center text-gray-700">
                      <svg className="w-5 h-5 text-dark-green mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      {benefit}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 bg-dark-green text-white">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-center mb-12"
          >
            Why Choose Professional Patio Installation
          </motion.h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-dark-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-1a1 1 0 011-1h2a1 1 0 011 1v1a1 1 0 001 1m-6 0h6" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold mb-3">Increased Property Value</h3>
              <p className="text-gray-200">
                Professional patio installation can increase your property value by 10-15% while providing
                immediate enjoyment and functionality.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-dark-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold mb-3">Year-Round Durability</h3>
              <p className="text-gray-200">
                Our patios are designed to withstand Boston's freeze-thaw cycles, heavy snow loads,
                and summer heat without cracking or shifting.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-dark-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold mb-3">Low Maintenance</h3>
              <p className="text-gray-200">
                Properly installed patios require minimal maintenance - just occasional cleaning
                and joint sand replenishment to maintain their beauty.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Our Custom Patio Installations
          </motion.h2>

          <Gallery images={galleryImages} />
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-8"
            >
              Ready to Create Your Dream Patio?
            </motion.h2>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-xl text-center mb-12 text-gray-700"
            >
              Contact us today for a free consultation and estimate for your custom patio installation project.
            </motion.p>

            <ContactForm />
          </div>
        </div>
      </section>

      {/* Local SEO Section */}
      <section className="py-8 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <p className="text-sm text-gray-600">
              <strong>American Elm Landscape</strong> - Professional custom patio installation serving Boston,
              Cambridge, Somerville, Brookline, Back Bay, South End, and surrounding areas. Licensed and insured
              contractors specializing in natural stone patios, paver installations, and decorative concrete for
              residential and commercial properties.
            </p>
          </div>
        </div>
      </section>
    </>
  );
};

export default CustomPatioInstallation;
