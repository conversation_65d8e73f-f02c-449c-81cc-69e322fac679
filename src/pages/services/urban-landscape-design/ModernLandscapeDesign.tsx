import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Gallery from '../../../components/ui/Gallery';
import ContactForm from '../../../components/forms/ContactForm';
import Button from '../../../components/ui/Button';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { services } from '../../../utils/constants';
import { landscapeImages } from '../../../utils/images';

const ModernLandscapeDesign: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Urban Landscape Design', url: 'https://americanelmlandscape.com/urban-landscape-design' },
    { name: 'Modern Landscape Design Boston', url: 'https://americanelmlandscape.com/urban-landscape-design/modern-landscape-design' }
  ];

  const modernFeatures = [
    {
      title: 'Clean Lines & Geometry',
      description: 'Crisp edges, geometric patterns, and structured layouts that complement contemporary architecture.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
        </svg>
      )
    },
    {
      title: 'Minimalist Plant Palette',
      description: 'Carefully curated selection of architectural plants with strong forms and year-round structure.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
        </svg>
      )
    },
    {
      title: 'Contemporary Materials',
      description: 'Modern materials like concrete, steel, and composite decking paired with natural stone and wood.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
        </svg>
      )
    },
    {
      title: 'Integrated Lighting',
      description: 'Sleek LED lighting systems that highlight architectural features and create dramatic nighttime effects.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
      )
    }
  ];

  const designPrinciples = [
    'Simplicity and restraint in plant selection',
    'Strong architectural framework',
    'Emphasis on texture and form over color',
    'Integration of indoor and outdoor spaces',
    'Sustainable and low-maintenance design',
    'Use of negative space as design element'
  ];

  const galleryImages = [
    {
      src: landscapeImages.hero.modern,
      alt: 'Modern landscape design Boston with clean lines',
      caption: 'Contemporary South End courtyard with geometric design'
    },
    {
      src: landscapeImages.gallery.modern,
      alt: 'Minimalist garden design Boston',
      caption: 'Minimalist rooftop garden in Seaport'
    },
    {
      src: landscapeImages.gallery.hardscape,
      alt: 'Modern hardscape design Boston',
      caption: 'Sleek concrete and steel hardscape elements'
    },
    {
      src: landscapeImages.gallery.lighting,
      alt: 'Contemporary landscape lighting',
      caption: 'Integrated LED lighting for modern aesthetics'
    },
    {
      src: landscapeImages.gallery.plants,
      alt: 'Architectural plants modern landscape',
      caption: 'Architectural plant selection with strong forms'
    }
  ];

  const relatedServices = services.urbanDesign.filter(service =>
    service.name !== 'Modern Landscape Design'
  ).slice(0, 3);

  return (
    <>
      <SEO
        title="Modern Landscape Design Boston | Contemporary Outdoor Spaces | American Elm Landscape"
        description="Contemporary modern landscape design in Boston. Clean lines, minimalist aesthetics, and innovative design for modern urban properties. Expert design-build services."
        keywords="modern landscape design Boston, contemporary landscaping, minimalist garden design, modern outdoor spaces, contemporary landscape architecture"
        canonical="https://americanelmlandscape.com/urban-landscape-design/modern-landscape-design"
        schema={[
          createServiceSchema(
            'Modern Landscape Design Boston',
            'Contemporary landscape design services featuring clean lines, minimalist aesthetics, and modern materials for Boston urban properties.',
            'Boston'
          ),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      {/* Hero Section */}
      <Hero
        title="Modern Landscape Design Boston"
        subtitle="Contemporary Design for Modern Urban Architecture"
        backgroundImage={landscapeImages.hero.modern}
        ctaText="Get Design Consultation"
        ctaLink="/contact"
        height="medium"
      />

      {/* Breadcrumbs */}
      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <Link to="/urban-landscape-design" className="text-dark-green hover:text-light-green">Urban Landscape Design</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Modern Landscape Design Boston</span>
          </nav>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-serif font-bold text-dark-green mb-8"
            >
              Modern Landscape Design Boston
            </motion.h1>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="prose prose-lg max-w-none text-gray-700 mb-12"
            >
              <p className="text-xl leading-relaxed mb-6">
                Transform your Boston property with modern landscape design that embraces clean lines, minimalist aesthetics,
                and contemporary materials. Our modern landscape design services create sophisticated outdoor spaces that
                complement today's urban architecture while providing functional, low-maintenance environments.
              </p>

              <h2 className="text-2xl font-serif font-semibold text-dark-green mb-4">
                Contemporary Design for Urban Boston
              </h2>

              <p className="leading-relaxed mb-6">
                Modern landscape design is perfectly suited to Boston's contemporary urban developments in neighborhoods
                like Seaport, South End, and Cambridge. Our approach emphasizes simplicity, functionality, and the thoughtful
                use of space—creating outdoor environments that feel both sophisticated and welcoming.
              </p>

              <p className="leading-relaxed mb-6">
                We specialize in designing modern landscapes that work with Boston's climate and urban constraints. From
                rooftop gardens to small courtyards, our designs maximize impact through careful material selection,
                architectural plant choices, and integrated lighting systems that create stunning spaces day and night.
              </p>

              <h3 className="text-xl font-semibold text-dark-green mb-4">
                Sustainable Modern Design
              </h3>

              <p className="leading-relaxed">
                Modern landscape design naturally aligns with sustainable practices. Our minimalist approach reduces
                maintenance requirements, conserves water through smart irrigation, and uses durable materials that
                stand the test of time. We create beautiful spaces that are both environmentally responsible and
                perfectly suited to contemporary urban living.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Modern Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Modern Landscape Design Elements
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {modernFeatures.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center bg-white p-6 rounded-lg shadow-lg"
              >
                <div className="w-16 h-16 bg-dark-green rounded-full flex items-center justify-center mx-auto mb-4 text-white">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">{feature.title}</h3>
                <p className="text-gray-700">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Design Principles Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-8"
            >
              Our Modern Design Principles
            </motion.h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {designPrinciples.map((principle, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="flex items-start"
                >
                  <svg className="w-6 h-6 text-dark-green mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-lg text-gray-700">{principle}</span>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Modern Landscape Portfolio
          </motion.h2>

          <Gallery images={galleryImages} />
        </div>
      </section>

      {/* Related Services */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Related Design Services
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {relatedServices.map((service, index) => (
              <motion.div
                key={service.path}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-lg shadow-lg p-6 text-center"
              >
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">
                  {service.name} Boston
                </h3>
                <p className="text-gray-700 mb-4">{service.description}</p>
                <Button to={service.path} variant="outline" size="sm">
                  Learn More
                </Button>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6">
                Ready for Modern Landscape Design?
              </h2>
              <p className="text-lg text-gray-700">
                Let's create a contemporary outdoor space that complements your modern Boston property.
                Contact us for a design consultation.
              </p>
            </motion.div>

            <ContactForm prefilledService="Modern Landscape Design" />
          </div>
        </div>
      </section>
    </>
  );
};

export default ModernLandscapeDesign;
