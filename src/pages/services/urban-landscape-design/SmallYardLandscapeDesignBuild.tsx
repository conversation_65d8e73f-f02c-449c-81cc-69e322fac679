import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { Sparkles } from 'lucide-react';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Gallery from '../../../components/ui/Gallery';
import ContactForm from '../../../components/forms/ContactForm';
import Button from '../../../components/ui/Button';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { services } from '../../../utils/constants';
import { landscapeImages } from '../../../utils/images';

const SmallYardLandscapeDesignBuild: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Urban Landscape Design', url: 'https://americanelmlandscape.com/urban-landscape-design' },
    { name: 'Small Yard Landscape Design Build Boston', url: 'https://americanelmlandscape.com/urban-landscape-design/small-yard-landscape-design-build' }
  ];

  const designStrategies = [
    {
      title: 'Vertical Gardening',
      description: 'Maximize planting space by using walls, trellises, and vertical structures for greenery and privacy.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 11l5-5m0 0l5 5m-5-5v12" />
        </svg>
      )
    },
    {
      title: 'Multi-Level Design',
      description: 'Create visual interest and functional zones through strategic use of elevation changes and terracing.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
        </svg>
      )
    },
    {
      title: 'Smart Storage',
      description: 'Integrate hidden storage solutions and dual-purpose features to keep small spaces organized and functional.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
        </svg>
      )
    },
    {
      title: 'Optical Illusions',
      description: 'Use design tricks like diagonal lines, mirrors, and strategic lighting to make spaces feel larger.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
        </svg>
      )
    }
  ];

  const smallSpaceSolutions = [
    'Compact patio and seating areas',
    'Built-in planters and raised beds',
    'Folding or stackable furniture',
    'Narrow walkways with purpose',
    'Dwarf and columnar plant varieties',
    'Reflective surfaces to enhance light',
    'Integrated lighting for ambiance',
    'Low-maintenance ground covers'
  ];

  const galleryImages = [
    {
      src: landscapeImages.hero.smallYard,
      alt: 'Small yard landscape design Boston',
      caption: 'Maximized small Back Bay courtyard'
    },
    {
      src: landscapeImages.gallery.compact,
      alt: 'Compact urban garden Boston',
      caption: 'Efficient use of limited space in South End'
    },
    {
      src: landscapeImages.gallery.vertical,
      alt: 'Vertical garden small yard',
      caption: 'Vertical gardening solution for tiny yard'
    },
    {
      src: landscapeImages.gallery.patio,
      alt: 'Small patio design Boston',
      caption: 'Intimate patio in compact urban space'
    },
    {
      src: landscapeImages.gallery.plants,
      alt: 'Small yard plantings',
      caption: 'Strategic plantings for small spaces'
    }
  ];

  const relatedServices = services.urbanDesign.filter(service =>
    service.name !== 'Small Yard Landscape Design Build'
  ).slice(0, 3);

  return (
    <>
      <SEO
        title="Small Yard Landscape Design Build Boston | Maximize Your Space | American Elm Landscape"
        description="Expert small yard landscape design and build services in Boston. Creative solutions to maximize small urban spaces and challenging lots. Transform your compact yard today."
        keywords="small yard landscaping Boston, small space design, urban yard design, compact landscape design, tiny yard solutions, small garden design"
        canonical="https://americanelmlandscape.com/urban-landscape-design/small-yard-landscape-design-build"
        schema={[
          createServiceSchema(
            'Small Yard Landscape Design Build Boston',
            'Specialized landscape design and build services for small urban yards in Boston, maximizing every square foot with creative solutions.',
            'Boston'
          ),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      {/* Hero Section */}
      <Hero
        title="Small Yard Landscape Design Build Boston"
        subtitle="Maximize Every Square Foot of Your Urban Property"
        backgroundImage={landscapeImages.hero.smallYard}
        ctaText="Get Design Ideas"
        ctaLink="/contact"
        height="medium"
      />

      {/* Breadcrumbs */}
      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <Link to="/urban-landscape-design" className="text-dark-green hover:text-light-green">Urban Landscape Design</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Small Yard Landscape Design Build Boston</span>
          </nav>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-serif font-bold text-dark-green mb-8"
            >
              Small Yard Landscape Design Build Boston
            </motion.h1>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="prose prose-lg max-w-none text-gray-700 mb-12"
            >
              <p className="text-xl leading-relaxed mb-6">
                Don't let limited space limit your outdoor potential. Our small yard landscape design and build services
                specialize in transforming compact Boston properties into beautiful, functional outdoor spaces. Through
                creative design strategies and expert installation, we maximize every square foot of your urban yard.
              </p>

              <h2 className="text-2xl font-serif font-semibold text-dark-green mb-4">
                Expertise in Small Urban Spaces
              </h2>

              <p className="leading-relaxed mb-6">
                Boston's urban neighborhoods are filled with small yards, narrow courtyards, and compact outdoor spaces.
                We specialize in these challenging properties, understanding how to create the illusion of space, maximize
                functionality, and incorporate all the features you want despite size constraints. From tiny Back Bay
                courtyards to narrow South End gardens, we've mastered the art of small-space design.
              </p>

              <p className="leading-relaxed mb-6">
                Our design-build approach ensures seamless execution from concept to completion. We handle everything—design,
                permits, demolition, installation, and planting—so you get a cohesive, professionally executed landscape
                that makes the most of your limited space.
              </p>

              <h3 className="text-xl font-semibold text-dark-green mb-4">
                Creative Solutions for Compact Yards
              </h3>

              <p className="leading-relaxed">
                Small yards require creative thinking and specialized expertise. We use vertical gardening, multi-level
                designs, compact plant varieties, and smart storage solutions to create outdoor spaces that feel spacious
                and inviting. Our designs incorporate privacy screening, outdoor living areas, and beautiful plantings—all
                within your compact footprint.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Design Strategies Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Small Space Design Strategies
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {designStrategies.map((strategy, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center bg-white p-6 rounded-lg shadow-lg"
              >
                <div className="w-16 h-16 bg-dark-green rounded-full flex items-center justify-center mx-auto mb-4 text-white">
                  {strategy.icon}
                </div>
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">{strategy.title}</h3>
                <p className="text-gray-700">{strategy.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Solutions Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-8"
            >
              Small Space Solutions We Provide
            </motion.h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {smallSpaceSolutions.map((solution, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="flex items-start"
                >
                  <svg className="w-6 h-6 text-dark-green mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-lg text-gray-700">{solution}</span>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 bg-dark-green text-white">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-center mb-12"
          >
            Why Choose Professional Small Yard Design
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <div className="text-4xl mb-4">📐</div>
              <h3 className="text-xl font-serif font-bold mb-3">Maximize Functionality</h3>
              <p className="text-gray-200">
                Expert design ensures every inch serves a purpose, creating surprisingly functional spaces
                from even the smallest yards.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-center"
            >
              <div className="inline-flex items-center justify-center w-20 h-20 bg-white/10 rounded-full mb-4">
                <Sparkles className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-xl font-serif font-bold mb-3">Create Visual Space</h3>
              <p className="text-gray-200">
                Professional design techniques make small yards feel larger through strategic use of lines,
                levels, and visual tricks.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-center"
            >
              <div className="text-4xl mb-4">💰</div>
              <h3 className="text-xl font-serif font-bold mb-3">Avoid Costly Mistakes</h3>
              <p className="text-gray-200">
                Small spaces leave no room for error. Professional design prevents expensive mistakes and
                ensures optimal use of your budget.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Small Yard Design Portfolio
          </motion.h2>

          <Gallery images={galleryImages} />
        </div>
      </section>

      {/* Related Services */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Related Design Services
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {relatedServices.map((service, index) => (
              <motion.div
                key={service.path}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-lg shadow-lg p-6 text-center"
              >
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">
                  {service.name} Boston
                </h3>
                <p className="text-gray-700 mb-4">{service.description}</p>
                <Button to={service.path} variant="outline" size="sm">
                  Learn More
                </Button>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6">
                Ready to Maximize Your Small Yard?
              </h2>
              <p className="text-lg text-gray-700">
                Let's create a design that makes the most of your compact space. Contact us for a free
                consultation and discover the potential of your small yard.
              </p>
            </motion.div>

            <ContactForm prefilledService="Small Yard Landscape Design Build" />
          </div>
        </div>
      </section>
    </>
  );
};

export default SmallYardLandscapeDesignBuild;
