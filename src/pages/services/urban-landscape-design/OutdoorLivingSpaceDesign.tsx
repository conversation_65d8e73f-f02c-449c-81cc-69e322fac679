import { motion } from 'framer-motion';
import { <PERSON> } from 'react-router-dom';
import { Home, Ruler, Blocks, Hammer } from 'lucide-react';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Gallery from '../../../components/ui/Gallery';
import ContactForm from '../../../components/forms/ContactForm';
import Button from '../../../components/ui/Button';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { services } from '../../../utils/constants';

const OutdoorLivingSpaceDesign: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Urban Landscape Design', url: 'https://americanelmlandscape.com/urban-landscape-design' },
    { name: 'Outdoor Living Space Design Boston', url: 'https://americanelmlandscape.com/urban-landscape-design/outdoor-living-space-design' }
  ];

  const processSteps = [
    {
      title: 'Site Assessment & Consultation',
      description: 'We evaluate your space, discuss your lifestyle needs, and understand your vision for the perfect outdoor living area.',
      icon: Home
    },
    {
      title: 'Custom Design Development',
      description: 'Our team creates detailed plans that maximize your space while incorporating your desired features and Boston building codes.',
      icon: Ruler
    },
    {
      title: 'Material Selection & Planning',
      description: 'We help you choose premium materials that withstand Boston weather while matching your aesthetic preferences.',
      icon: Blocks
    },
    {
      title: 'Professional Installation',
      description: 'Expert installation with attention to detail, ensuring your outdoor living space is built to last.',
      icon: Hammer
    }
  ];

  const galleryImages = [
    {
      src: '/images/outdoor-living-complete.jpg',
      alt: 'Complete outdoor living space design in Boston with seating and fire pit',
      caption: 'Multi-functional outdoor living space in Back Bay'
    },
    {
      src: '/images/outdoor-living-kitchen.jpg',
      alt: 'Outdoor living space with kitchen area in Boston',
      caption: 'Outdoor kitchen integration in Cambridge'
    },
    {
      src: '/images/outdoor-living-seating.jpg',
      alt: 'Comfortable seating area in outdoor living space',
      caption: 'Cozy seating area with privacy screening'
    },
    {
      src: '/images/outdoor-living-lighting.jpg',
      alt: 'Outdoor living space with evening lighting',
      caption: 'Evening ambiance with professional lighting'
    },
    {
      src: '/images/outdoor-living-plants.jpg',
      alt: 'Outdoor living space with integrated landscaping',
      caption: 'Integrated plantings for year-round interest'
    }
  ];

  const relatedServices = services.urbanDesign.filter(service => 
    service.name !== 'Outdoor Living Space Design'
  ).slice(0, 3);

  return (
    <>
      <SEO
        title="Outdoor Living Space Design Boston | Custom Design & Build | American Elm Landscape"
        description="Transform your Boston property into a comprehensive outdoor living space. Expert design and installation for patios, seating areas, kitchens, and more. Free consultation."
        keywords="outdoor living space design Boston, outdoor room design, patio design Boston, outdoor kitchen design, backyard design Boston"
        canonical="https://americanelmlandscape.com/urban-landscape-design/outdoor-living-space-design"
        schema={[
          createServiceSchema(
            'Outdoor Living Space Design Boston',
            'Professional outdoor living space design and installation services for Boston area properties, creating functional and beautiful outdoor rooms.',
            'Boston'
          ),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      {/* Hero Section */}
      <Hero
        title="Outdoor Living Space Design Boston"
        subtitle="Create Your Perfect Outdoor Room in the Heart of the City"
        backgroundImage="https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80"
        ctaText="Design My Space"
        ctaLink="/contact"
        height="medium"
      />

      {/* Breadcrumbs */}
      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <Link to="/urban-landscape-design" className="text-dark-green hover:text-light-green">Urban Landscape Design</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Outdoor Living Space Design Boston</span>
          </nav>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-serif font-bold text-dark-green mb-8"
            >
              Outdoor Living Space Design Boston
            </motion.h1>
            
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="prose prose-lg max-w-none text-gray-700 mb-12"
            >
              <p className="text-xl leading-relaxed mb-6">
                Transform your Boston property into a comprehensive outdoor living space that extends your home's 
                functionality into the outdoors. Our outdoor living space design services create seamless transitions 
                between indoor and outdoor areas, maximizing your property's potential for entertainment, relaxation, and daily living.
              </p>
              
              <h2 className="text-2xl font-serif font-semibold text-dark-green mb-4">
                Comprehensive Outdoor Room Design
              </h2>
              
              <p className="leading-relaxed mb-6">
                In Boston's dense urban environment, outdoor living spaces serve as valuable extensions of your home. 
                Whether you're working with a small Back Bay courtyard or a larger Cambridge backyard, we design 
                outdoor rooms that include multiple functional zones: dining areas, lounging spaces, cooking areas, 
                and garden features.
              </p>
              
              <p className="leading-relaxed mb-6">
                Our designs consider Boston's unique climate challenges, incorporating weather-resistant materials, 
                proper drainage, and seasonal interest. We understand local building codes and work within neighborhood 
                character guidelines to create spaces that enhance your property value while respecting the surrounding environment.
              </p>

              <h3 className="text-xl font-semibold text-dark-green mb-4">
                Local Expertise for Boston Properties
              </h3>
              
              <p className="leading-relaxed">
                Boston's historic neighborhoods require specialized knowledge of soil conditions, drainage patterns, 
                and architectural compatibility. Our team has extensive experience working with Victorian brownstones 
                in South End, modern condos in Seaport, and everything in between. We know how to maximize small spaces 
                while creating privacy and functionality in dense urban settings.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Our Design Process
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {processSteps.map((step, index) => {
              const IconComponent = step.icon;
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="text-center"
                >
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-dark-green/10 rounded-full mb-4">
                    <IconComponent className="w-8 h-8 text-dark-green" />
                  </div>
                  <h3 className="text-xl font-semibold text-dark-green mb-3">{step.title}</h3>
                  <p className="text-gray-700">{step.description}</p>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-3xl font-serif font-bold text-dark-green mb-6">
                Benefits of Professional Outdoor Living Design
              </h2>
              
              <ul className="space-y-4 text-gray-700">
                <li className="flex items-start">
                  <svg className="w-6 h-6 text-dark-green mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span><strong>Increased Property Value:</strong> Well-designed outdoor living spaces can add 15-20% to your home's value</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-6 h-6 text-dark-green mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span><strong>Extended Living Space:</strong> Effectively doubles your usable living area during good weather</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-6 h-6 text-dark-green mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span><strong>Year-Round Interest:</strong> Designed for Boston's four seasons with winter structure and summer color</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-6 h-6 text-dark-green mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span><strong>Low Maintenance:</strong> Smart plant selection and materials reduce ongoing maintenance needs</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-6 h-6 text-dark-green mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span><strong>Privacy & Comfort:</strong> Strategic design creates intimate spaces in urban environments</span>
                </li>
              </ul>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Gallery images={galleryImages} columns={2} />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Related Services */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Related Urban Design Services
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {relatedServices.map((service, index) => (
              <motion.div
                key={service.path}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-lg shadow-lg p-6 text-center"
              >
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">
                  {service.name} Boston
                </h3>
                <p className="text-gray-700 mb-4">{service.description}</p>
                <Button to={service.path} variant="outline" size="sm">
                  Learn More
                </Button>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6">
                Ready to Design Your Outdoor Living Space?
              </h2>
              <p className="text-lg text-gray-700">
                Let's create the perfect outdoor room for your Boston property. Our team will work with you 
                to design a space that fits your lifestyle and maximizes your outdoor potential.
              </p>
            </motion.div>
            
            <ContactForm prefilledService="Outdoor Living Space Design" />
          </div>
        </div>
      </section>
    </>
  );
};

export default OutdoorLivingSpaceDesign;
