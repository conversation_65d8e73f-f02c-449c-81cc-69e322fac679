import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Gallery from '../../../components/ui/Gallery';
import ContactForm from '../../../components/forms/ContactForm';
import Button from '../../../components/ui/Button';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { services } from '../../../utils/constants';
import { landscapeImages } from '../../../utils/images';

const UrbanBackyardTransformation: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Urban Landscape Design', url: 'https://americanelmlandscape.com/urban-landscape-design' },
    { name: 'Urban Backyard Transformation Boston', url: 'https://americanelmlandscape.com/urban-landscape-design/urban-backyard-transformation' }
  ];

  const transformationSteps = [
    {
      title: 'Initial Assessment',
      description: 'Comprehensive evaluation of your current space, including soil conditions, drainage, sunlight, and structural constraints.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
        </svg>
      )
    },
    {
      title: 'Custom Design',
      description: 'Collaborative design process creating a detailed plan that maximizes your space and meets your lifestyle needs.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
        </svg>
      )
    },
    {
      title: 'Complete Demolition',
      description: 'Professional removal of old features, plants, and hardscaping to prepare for your new landscape.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
        </svg>
      )
    },
    {
      title: 'Expert Installation',
      description: 'Professional installation of all hardscape, plantings, lighting, and features to bring your vision to life.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5" />
        </svg>
      )
    }
  ];

  const transformationFeatures = [
    'Complete site demolition and preparation',
    'Custom hardscape design and installation',
    'Strategic plant selection for urban conditions',
    'Integrated lighting systems',
    'Drainage and irrigation solutions',
    'Privacy screening and fencing',
    'Outdoor living spaces and patios',
    'Low-maintenance design approach'
  ];

  const galleryImages = [
    {
      src: landscapeImages.hero.transformation,
      alt: 'Urban backyard transformation Boston before and after',
      caption: 'Complete South End backyard transformation'
    },
    {
      src: landscapeImages.gallery.beforeAfter,
      alt: 'Boston backyard makeover',
      caption: 'Cambridge small yard complete renovation'
    },
    {
      src: landscapeImages.gallery.patio,
      alt: 'Urban backyard with new patio',
      caption: 'New patio and outdoor living space'
    },
    {
      src: landscapeImages.gallery.plants,
      alt: 'Transformed backyard with plantings',
      caption: 'Lush plantings in transformed urban yard'
    },
    {
      src: landscapeImages.gallery.lighting,
      alt: 'Backyard transformation with lighting',
      caption: 'Evening ambiance in transformed space'
    }
  ];

  const relatedServices = services.urbanDesign.filter(service =>
    service.name !== 'Urban Backyard Transformation'
  ).slice(0, 3);

  return (
    <>
      <SEO
        title="Urban Backyard Transformation Boston | Complete Makeovers | American Elm Landscape"
        description="Complete urban backyard transformations in Boston. Expert design and build services for small city yards and challenging urban spaces. Transform your outdoor space today."
        keywords="urban backyard transformation Boston, backyard makeover, small yard renovation, urban landscaping, backyard redesign, complete landscape renovation"
        canonical="https://americanelmlandscape.com/urban-landscape-design/urban-backyard-transformation"
        schema={[
          createServiceSchema(
            'Urban Backyard Transformation Boston',
            'Complete backyard transformation services for Boston urban properties, including design, demolition, and professional installation.',
            'Boston'
          ),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      {/* Hero Section */}
      <Hero
        title="Urban Backyard Transformation Boston"
        subtitle="Complete Makeovers for Boston's Urban Backyards"
        backgroundImage={landscapeImages.hero.transformation}
        ctaText="Start Your Transformation"
        ctaLink="/contact"
        height="medium"
      />

      {/* Breadcrumbs */}
      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <Link to="/urban-landscape-design" className="text-dark-green hover:text-light-green">Urban Landscape Design</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Urban Backyard Transformation Boston</span>
          </nav>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-serif font-bold text-dark-green mb-8"
            >
              Urban Backyard Transformation Boston
            </motion.h1>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="prose prose-lg max-w-none text-gray-700 mb-12"
            >
              <p className="text-xl leading-relaxed mb-6">
                Transform your neglected or underutilized Boston backyard into a stunning outdoor oasis. Our complete
                backyard transformation services take your space from concept to completion, handling every detail of
                the design and build process to create the outdoor environment you've always dreamed of.
              </p>

              <h2 className="text-2xl font-serif font-semibold text-dark-green mb-4">
                Complete Makeovers for Urban Boston Yards
              </h2>

              <p className="leading-relaxed mb-6">
                Boston's urban backyards present unique challenges—small footprints, poor drainage, limited sunlight,
                and aging infrastructure. Our transformation services address all these issues while creating beautiful,
                functional outdoor spaces. Whether you're in South End, Cambridge, or Somerville, we specialize in
                maximizing the potential of challenging urban lots.
              </p>

              <p className="leading-relaxed mb-6">
                We handle everything from initial demolition to final planting, including hardscaping, drainage systems,
                lighting, irrigation, and custom features. Our comprehensive approach ensures every element works together
                to create a cohesive, beautiful space that enhances your property value and quality of life.
              </p>

              <h3 className="text-xl font-semibold text-dark-green mb-4">
                Expertise in Boston's Urban Challenges
              </h3>

              <p className="leading-relaxed">
                Our team understands Boston's unique soil conditions, drainage patterns, and climate challenges. We work
                within local building codes and neighborhood guidelines while creating transformative designs that respect
                the character of your area. From Victorian brownstone backyards to modern condo courtyards, we've
                successfully transformed hundreds of Boston urban spaces.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Transformation Process Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Our Transformation Process
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {transformationSteps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center bg-white p-6 rounded-lg shadow-lg"
              >
                <div className="w-16 h-16 bg-dark-green rounded-full flex items-center justify-center mx-auto mb-4 text-white">
                  {step.icon}
                </div>
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">{step.title}</h3>
                <p className="text-gray-700">{step.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-8"
            >
              What's Included in Your Transformation
            </motion.h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {transformationFeatures.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="flex items-start"
                >
                  <svg className="w-6 h-6 text-dark-green mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-lg text-gray-700">{feature}</span>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 bg-dark-green text-white">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-center mb-12"
          >
            Benefits of Complete Backyard Transformation
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <div className="text-4xl mb-4">🏡</div>
              <h3 className="text-xl font-serif font-bold mb-3">Increased Home Value</h3>
              <p className="text-gray-200">
                Professional landscape transformations can increase property value by 15-20% while providing
                immediate enjoyment of your outdoor space.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-center"
            >
              <div className="text-4xl mb-4">⚡</div>
              <h3 className="text-xl font-serif font-bold mb-3">Turnkey Solution</h3>
              <p className="text-gray-200">
                One team handles everything from design to installation, ensuring seamless coordination and
                eliminating the hassle of managing multiple contractors.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-center"
            >
              <div className="text-4xl mb-4">🌿</div>
              <h3 className="text-xl font-serif font-bold mb-3">Year-Round Beauty</h3>
              <p className="text-gray-200">
                Thoughtful design creates outdoor spaces that look beautiful in all four seasons while
                requiring minimal maintenance.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Transformation Portfolio
          </motion.h2>

          <Gallery images={galleryImages} />
        </div>
      </section>

      {/* Related Services */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Related Design Services
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {relatedServices.map((service, index) => (
              <motion.div
                key={service.path}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-lg shadow-lg p-6 text-center"
              >
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">
                  {service.name} Boston
                </h3>
                <p className="text-gray-700 mb-4">{service.description}</p>
                <Button to={service.path} variant="outline" size="sm">
                  Learn More
                </Button>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6">
                Ready to Transform Your Backyard?
              </h2>
              <p className="text-lg text-gray-700">
                Let's discuss your vision and create a complete transformation plan for your Boston backyard.
                Contact us for a free consultation.
              </p>
            </motion.div>

            <ContactForm prefilledService="Urban Backyard Transformation" />
          </div>
        </div>
      </section>
    </>
  );
};

export default UrbanBackyardTransformation;
