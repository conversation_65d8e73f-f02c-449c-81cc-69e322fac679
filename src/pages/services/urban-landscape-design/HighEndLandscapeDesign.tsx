import { motion } from 'framer-motion';
import { <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON>, <PERSON>, Star } from 'lucide-react';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Gallery from '../../../components/ui/Gallery';
import ContactForm from '../../../components/forms/ContactForm';
import Button from '../../../components/ui/Button';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { services } from '../../../utils/constants';
import { landscapeImages } from '../../../utils/images';

const HighEndLandscapeDesign: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Urban Landscape Design', url: 'https://americanelmlandscape.com/urban-landscape-design' },
    { name: 'High End Landscape Design Boston', url: 'https://americanelmlandscape.com/urban-landscape-design/high-end-landscape-design' }
  ];

  const luxuryFeatures = [
    {
      title: 'Premium Material Selection',
      description: 'Imported natural stone, rare plants, and custom-crafted elements that create truly unique outdoor spaces.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
        </svg>
      )
    },
    {
      title: 'Bespoke Design Process',
      description: 'Collaborative design process with 3D renderings, material samples, and detailed planning for your vision.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
      )
    },
    {
      title: 'Master Craftmanship',
      description: 'Skilled artisans and certified installers who bring decades of experience to every project detail.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
        </svg>
      )
    },
    {
      title: 'Ongoing Maintenance',
      description: 'Comprehensive maintenance programs to preserve the beauty and investment of your luxury landscape.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    }
  ];

  const designElements = [
    {
      category: 'Hardscape Features',
      items: ['Natural stone terraces', 'Custom water features', 'Outdoor fireplaces', 'Artisan stonework', 'Luxury outdoor kitchens']
    },
    {
      category: 'Plant Design',
      items: ['Rare specimen trees', 'Seasonal color programs', 'Architectural plantings', 'Privacy screening', 'Fragrant gardens']
    },
    {
      category: 'Lighting & Technology',
      items: ['Architectural lighting', 'Smart irrigation systems', 'Outdoor audio systems', 'Security integration', 'Climate control']
    }
  ];

  const galleryImages = [
    {
      src: landscapeImages.hero.luxury,
      alt: 'High-end landscape design Boston with luxury materials',
      caption: 'Luxury Back Bay rooftop garden with premium materials'
    },
    {
      src: landscapeImages.gallery.luxury,
      alt: 'Sophisticated landscape architecture Boston',
      caption: 'Sophisticated Cambridge estate landscape'
    },
    {
      src: landscapeImages.gallery.waterFeature,
      alt: 'Custom water feature in luxury landscape',
      caption: 'Custom water feature with natural stone'
    },
    {
      src: landscapeImages.gallery.lighting,
      alt: 'Luxury landscape lighting Boston',
      caption: 'Architectural lighting for evening ambiance'
    },
    {
      src: landscapeImages.gallery.plants,
      alt: 'Premium plant selection luxury landscaping',
      caption: 'Curated plant selection for year-round interest'
    }
  ];

  const relatedServices = services.urbanDesign.filter(service =>
    service.name !== 'High End Landscape Design'
  ).slice(0, 3);

  return (
    <>
      <SEO
        title="High End Landscape Design Boston | Luxury Outdoor Spaces | American Elm Landscape"
        description="Luxury high-end landscape design services in Boston. Premium materials, expert craftsmanship, and sophisticated design for discerning homeowners seeking exceptional outdoor spaces."
        keywords="high end landscape design Boston, luxury landscaping, premium outdoor design, sophisticated landscape architecture, luxury garden design"
        canonical="https://americanelmlandscape.com/urban-landscape-design/high-end-landscape-design"
        schema={[
          createServiceSchema(
            'High End Landscape Design Boston',
            'Luxury landscape design services for discerning Boston homeowners, featuring premium materials, bespoke design, and master craftsmanship.',
            'Boston'
          ),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      {/* Hero Section */}
      <Hero
        title="High End Landscape Design Boston"
        subtitle="Luxury Landscape Design for Discerning Boston Homeowners"
        backgroundImage={landscapeImages.hero.luxury}
        ctaText="Schedule Consultation"
        ctaLink="/contact"
        height="medium"
      />

      {/* Breadcrumbs */}
      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <Link to="/urban-landscape-design" className="text-dark-green hover:text-light-green">Urban Landscape Design</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">High End Landscape Design Boston</span>
          </nav>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-serif font-bold text-dark-green mb-8"
            >
              High End Landscape Design Boston
            </motion.h1>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="prose prose-lg max-w-none text-gray-700 mb-12"
            >
              <p className="text-xl leading-relaxed mb-6">
                Elevate your Boston property with luxury landscape design that reflects your sophisticated taste and lifestyle.
                Our high-end landscape design services combine premium materials, innovative design concepts, and master
                craftsmanship to create extraordinary outdoor spaces that serve as natural extensions of your home.
              </p>

              <h2 className="text-2xl font-serif font-semibold text-dark-green mb-4">
                Bespoke Design for Boston's Elite Properties
              </h2>

              <p className="leading-relaxed mb-6">
                From historic Beacon Hill townhouses to contemporary Back Bay penthouses, we understand that luxury
                landscape design requires more than beautiful plants and expensive materials. It demands a deep understanding
                of your lifestyle, architectural context, and the unique challenges of Boston's urban environment.
              </p>

              <p className="leading-relaxed mb-6">
                Our approach begins with understanding your vision and lifestyle needs, then translates them into sophisticated
                outdoor environments that provide privacy, beauty, and functionality. We source premium materials from around
                the world and work with master craftsmen to ensure every detail meets the highest standards of quality and design.
              </p>

              <h3 className="text-xl font-semibold text-dark-green mb-4">
                Luxury Design for Boston's Climate
              </h3>

              <p className="leading-relaxed">
                High-end landscape design in Boston requires expertise in creating year-round beauty despite harsh winters
                and variable weather. We select premium plant materials that provide four-season interest, design sophisticated
                drainage systems, and choose materials that age gracefully in New England's climate while maintaining their
                luxury appeal.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Luxury Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Luxury Landscape Design Features
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {luxuryFeatures.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center bg-white p-6 rounded-lg shadow-lg"
              >
                <div className="w-16 h-16 bg-dark-green rounded-full flex items-center justify-center mx-auto mb-4 text-white">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">{feature.title}</h3>
                <p className="text-gray-700">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Design Elements Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Premium Design Elements
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {designElements.map((element, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white p-6 rounded-lg shadow-lg"
              >
                <h3 className="text-2xl font-serif font-bold text-dark-green mb-4">{element.category}</h3>
                <ul className="space-y-2">
                  {element.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start text-gray-700">
                      <svg className="w-5 h-5 text-dark-green mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 bg-dark-green text-white">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-center mb-12"
          >
            The Value of Luxury Landscape Design
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <div className="inline-flex items-center justify-center w-20 h-20 bg-white/10 rounded-full mb-4">
                <Gem className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-xl font-serif font-bold mb-3">Exceptional Property Value</h3>
              <p className="text-gray-200">
                Luxury landscape design can increase property value by 20-30%, providing both immediate enjoyment
                and long-term investment returns.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-center"
            >
              <div className="inline-flex items-center justify-center w-20 h-20 bg-white/10 rounded-full mb-4">
                <Trophy className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-xl font-serif font-bold mb-3">Distinctive Character</h3>
              <p className="text-gray-200">
                Custom design and rare materials create a truly unique outdoor space that reflects your personal
                style and sets your property apart.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-center"
            >
              <div className="inline-flex items-center justify-center w-20 h-20 bg-white/10 rounded-full mb-4">
                <Star className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-xl font-serif font-bold mb-3">Timeless Beauty</h3>
              <p className="text-gray-200">
                Premium materials and expert craftsmanship ensure your landscape remains beautiful and functional
                for decades to come.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Luxury Landscape Portfolio
          </motion.h2>

          <Gallery images={galleryImages} />
        </div>
      </section>

      {/* Related Services */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Related Design Services
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {relatedServices.map((service, index) => (
              <motion.div
                key={service.path}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-lg shadow-lg p-6 text-center"
              >
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">
                  {service.name} Boston
                </h3>
                <p className="text-gray-700 mb-4">{service.description}</p>
                <Button to={service.path} variant="outline" size="sm">
                  Learn More
                </Button>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6">
                Ready to Create Your Luxury Landscape?
              </h2>
              <p className="text-lg text-gray-700">
                Let's discuss your vision for a truly exceptional outdoor space. Our team will work with you
                to design a luxury landscape that exceeds your expectations.
              </p>
            </motion.div>

            <ContactForm prefilledService="High End Landscape Design" />
          </div>
        </div>
      </section>

      {/* Local SEO Section */}
      <section className="py-8 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <p className="text-sm text-gray-600">
              <strong>American Elm Landscape</strong> - Luxury high-end landscape design serving Boston,
              Beacon Hill, Back Bay, South End, Cambridge, Brookline, and surrounding elite neighborhoods.
              Premium materials, bespoke design, and master craftsmanship for discerning homeowners seeking
              exceptional outdoor spaces.
            </p>
          </div>
        </div>
      </section>
    </>
  );
};

export default HighEndLandscapeDesign;
