import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { Home, Building2, Sliders, Sailboat } from 'lucide-react';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Gallery from '../../../components/ui/Gallery';
import ContactForm from '../../../components/forms/ContactForm';
import Button from '../../../components/ui/Button';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { services } from '../../../utils/constants';
import { landscapeImages } from '../../../utils/images';

const DeckPatioCoverInstallation: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Outdoor Living Features', url: 'https://americanelmlandscape.com/outdoor-living-features' },
    { name: 'Deck & Patio Cover Installation Boston', url: 'https://americanelmlandscape.com/outdoor-living-features/deck-patio-cover-installation' }
  ];

  const coverTypes = [
    { title: 'Retractable Awnings', description: 'Flexible shade solutions with motorized or manual operation', icon: Home },
    { title: 'Fixed Roof Covers', description: 'Permanent protection from sun and rain', icon: Building2 },
    { title: 'Louvered Systems', description: 'Adjustable louvers for customizable shade and ventilation', icon: Sliders },
    { title: 'Shade Sails', description: 'Modern, architectural shade solutions', icon: Sailboat }
  ];

  const benefits = ['Protection from sun and rain', 'Extends outdoor season', 'Reduces UV exposure', 'Protects outdoor furniture', 'Lowers cooling costs', 'Increases usable space', 'Adds property value', 'Customizable aesthetics'];
  const galleryImages = [
    { src: landscapeImages.gallery.patioCover, alt: 'Patio cover installation Boston', caption: 'Custom patio cover in Back Bay' },
    { src: landscapeImages.gallery.deckCover, alt: 'Deck cover', caption: 'Retractable awning over deck' },
    { src: landscapeImages.gallery.louveredRoof, alt: 'Louvered roof system', caption: 'Adjustable louvered patio cover' },
    { src: landscapeImages.gallery.shadeSail, alt: 'Shade sail', caption: 'Modern shade sail installation' }
  ];

  const relatedServices = services.outdoorLiving.filter(service => service.name !== 'Deck & Patio Cover Installation').slice(0, 3);

  return (
    <>
      <SEO
        title="Deck & Patio Cover Installation Boston | Awnings & Shade Solutions | American Elm Landscape"
        description="Professional deck and patio cover installation in Boston. Retractable awnings, fixed covers, and louvered systems. Extend your outdoor living season."
        keywords="patio cover installation Boston, deck cover, retractable awnings, patio awning, outdoor shade, louvered roof"
        canonical="https://americanelmlandscape.com/outdoor-living-features/deck-patio-cover-installation"
        schema={[
          createServiceSchema('Deck & Patio Cover Installation Boston', 'Professional deck and patio cover installation services for Boston properties, providing shade and weather protection.', 'Boston'),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      <Hero title="Deck & Patio Cover Installation Boston" subtitle="Extend Your Outdoor Season with Professional Shade Solutions" backgroundImage={landscapeImages.hero.patioCover} ctaText="Explore Options" ctaLink="/contact" height="medium" />

      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <Link to="/outdoor-living-features" className="text-dark-green hover:text-light-green">Outdoor Living Features</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Deck & Patio Cover Installation Boston</span>
          </nav>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h1 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-4xl md:text-5xl font-serif font-bold text-dark-green mb-8">
              Deck & Patio Cover Installation Boston
            </motion.h1>
            <motion.div initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: 0.2 }} className="prose prose-lg max-w-none text-gray-700 mb-12">
              <p className="text-xl leading-relaxed mb-6">
                Extend your outdoor living season and protect your Boston deck or patio with professional cover installation. From retractable awnings to permanent roof systems, we provide shade solutions that enhance comfort, protect your investment, and allow you to enjoy your outdoor space in any weather.
              </p>
              <h2 className="text-2xl font-serif font-semibold text-dark-green mb-4">Professional Shade Solutions for Boston Properties</h2>
              <p className="leading-relaxed mb-6">
                Deck and patio covers transform outdoor spaces by providing protection from sun and rain, reducing UV exposure, and creating comfortable areas for entertaining and relaxation. We install a variety of cover systems—from motorized retractable awnings to fixed louvered roofs—each designed to complement your home's architecture while providing reliable performance in Boston's varied weather.
              </p>
              <p className="leading-relaxed">
                Our installation includes proper structural support, weatherproofing, and professional finishing. We handle permits, electrical work for motorized systems, and integration with your existing deck or patio for a seamless, beautiful result that adds value and functionality to your property.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12">
            Cover System Options
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {coverTypes.map((type, index) => {
              const IconComponent = type.icon;
              return (
                <motion.div key={index} initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="text-center bg-white p-6 rounded-lg shadow-lg">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-dark-green/10 rounded-full mb-4">
                    <IconComponent className="w-8 h-8 text-dark-green" />
                  </div>
                  <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">{type.title}</h3>
                  <p className="text-gray-700">{type.description}</p>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-8">
              Benefits of Patio Covers
            </motion.h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {benefits.map((benefit, index) => (
                <motion.div key={index} initial={{ opacity: 0, x: -20 }} whileInView={{ opacity: 1, x: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="flex items-start">
                  <svg className="w-6 h-6 text-dark-green mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-lg text-gray-700">{benefit}</span>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12">
            Patio Cover Portfolio
          </motion.h2>
          <Gallery images={galleryImages} />
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl font-serif font-bold text-dark-green text-center mb-12">
            Related Outdoor Living Services
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {relatedServices.map((service, index) => (
              <motion.div key={service.path} initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="bg-white rounded-lg shadow-lg p-6 text-center">
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">{service.name} Boston</h3>
                <p className="text-gray-700 mb-4">{service.description}</p>
                <Button to={service.path} variant="outline" size="sm">Learn More</Button>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6">Ready for a Patio Cover?</h2>
              <p className="text-lg text-gray-700">Contact us for a free consultation and explore the best shade solution for your outdoor space.</p>
            </motion.div>
            <ContactForm prefilledService="Deck & Patio Cover Installation" />
          </div>
        </div>
      </section>
    </>
  );
};

export default DeckPatioCoverInstallation;
