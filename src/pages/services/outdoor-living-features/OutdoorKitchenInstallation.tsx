import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Gallery from '../../../components/ui/Gallery';
import ContactForm from '../../../components/forms/ContactForm';
import Button from '../../../components/ui/Button';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { landscapeImages } from '../../../utils/images';

const OutdoorKitchenInstallation: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Outdoor Living Features', url: 'https://americanelmlandscape.com/outdoor-living-features' },
    { name: 'Outdoor Kitchen Installation Boston', url: 'https://americanelmlandscape.com/services/outdoor-living-features/outdoor-kitchen-installation' }
  ];

  const processSteps = [
    {
      title: 'Design Consultation & Planning',
      description: 'We assess your space, discuss your cooking and entertaining needs, and create a custom design that maximizes functionality.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v6a2 2 0 002 2h2m0-8h2a2 2 0 012 2v6a2 2 0 01-2 2H9m0-8v8" />
        </svg>
      )
    },
    {
      title: 'Utility Planning & Permits',
      description: 'Professional planning for gas, electrical, and plumbing connections with all necessary permits and code compliance.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
      )
    },
    {
      title: 'Foundation & Structure Build',
      description: 'Construction of proper foundations, framework, and structural elements designed to support appliances and withstand weather.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
      )
    },
    {
      title: 'Appliance & Finishing Installation',
      description: 'Professional installation of appliances, countertops, cabinetry, and finishing touches for a complete outdoor kitchen.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
        </svg>
      )
    }
  ];

  const kitchenFeatures = [
    {
      title: 'Premium Appliances',
      description: 'High-end outdoor grills, refrigerators, ice makers, and cooking equipment designed for outdoor use.',
      benefits: ['Weather-resistant construction', 'Professional-grade performance', 'Energy efficient operation', 'Extended warranties']
    },
    {
      title: 'Custom Countertops',
      description: 'Durable countertop materials including granite, quartz, and concrete designed for outdoor conditions.',
      benefits: ['Heat and stain resistant', 'Easy to clean and maintain', 'Beautiful natural patterns', 'Long-lasting durability']
    },
    {
      title: 'Weather-Resistant Cabinetry',
      description: 'Custom outdoor cabinetry with marine-grade finishes and stainless steel hardware.',
      benefits: ['Moisture resistant materials', 'Corrosion-proof hardware', 'Ample storage space', 'Custom configurations']
    }
  ];

  const galleryImages = [
    {
      src: landscapeImages.hero.outdoorLiving,
      alt: 'Custom outdoor kitchen installation Boston',
      caption: 'Complete outdoor kitchen with dining area and fire feature'
    },
    {
      src: landscapeImages.gallery.lighting,
      alt: 'Outdoor kitchen with professional lighting',
      caption: 'Professional lighting enhances evening entertaining'
    },
    {
      src: landscapeImages.gallery.waterFeature,
      alt: 'Luxury outdoor kitchen with water feature',
      caption: 'Luxury outdoor kitchen with integrated water feature'
    },
    {
      src: landscapeImages.gallery.pergola,
      alt: 'Covered outdoor kitchen Boston',
      caption: 'Covered outdoor kitchen for year-round use'
    }
  ];

  return (
    <>
      <SEO
        title="Outdoor Kitchen Installation Boston | Custom Outdoor Cooking Spaces | American Elm Landscape"
        description="Professional outdoor kitchen installation in Boston. Custom outdoor cooking spaces with premium appliances, countertops, and weather-resistant materials for year-round entertainment."
        keywords="outdoor kitchen installation Boston, outdoor cooking spaces, backyard kitchen, outdoor kitchen design, custom outdoor kitchens, outdoor appliances Boston"
        canonical="https://americanelmlandscape.com/outdoor-living-features/outdoor-kitchen-installation"
        schema={[
          createServiceSchema(
            'Outdoor Kitchen Installation Boston',
            'Professional outdoor kitchen installation services for Boston area properties with custom design, premium appliances, and weather-resistant materials.'
          ),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      {/* Hero Section */}
      <Hero
        title="Outdoor Kitchen Installation Boston"
        subtitle="Custom Outdoor Cooking Spaces for Year-Round Entertainment"
        backgroundImage={landscapeImages.hero.outdoorLiving}
        ctaText="Start Planning"
        ctaLink="/contact"
        height="medium"
      />

      {/* Breadcrumbs */}
      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <Link to="/outdoor-living-features" className="text-dark-green hover:text-light-green">Outdoor Living Features</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Outdoor Kitchen Installation Boston</span>
          </nav>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6"
            >
              Create the Ultimate Outdoor Cooking & Entertainment Space
            </motion.h2>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="prose prose-lg max-w-none text-gray-700"
            >
              <p className="text-xl leading-relaxed mb-6">
                Transform your Boston outdoor space into a culinary paradise with a custom outdoor kitchen installation.
                Our expert team designs and builds complete outdoor cooking spaces that extend your home's functionality
                and create the perfect setting for entertaining family and friends year-round.
              </p>

              <p className="text-lg leading-relaxed mb-6">
                From intimate Back Bay courtyards to expansive Brookline estates, we create outdoor kitchens that
                complement your home's architecture while providing all the amenities of an indoor kitchen. Our
                installations feature premium appliances, durable countertops, and weather-resistant cabinetry
                designed to withstand Boston's challenging climate.
              </p>

              <p className="text-lg leading-relaxed">
                Whether you're planning casual family barbecues or elegant dinner parties, our custom outdoor
                kitchens provide the functionality, style, and durability you need to make every outdoor gathering
                memorable while significantly increasing your property value.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Our Outdoor Kitchen Installation Process
          </motion.h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
            {processSteps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-dark-green rounded-full flex items-center justify-center mx-auto mb-4 text-white">
                  {step.icon}
                </div>
                <h3 className="text-xl font-serif font-bold text-dark-green mb-3">{step.title}</h3>
                <p className="text-gray-600">{step.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Premium Outdoor Kitchen Components
          </motion.h2>

          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {kitchenFeatures.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white p-6 rounded-lg shadow-lg"
              >
                <h3 className="text-2xl font-serif font-bold text-dark-green mb-4">{feature.title}</h3>
                <p className="text-gray-600 mb-6">{feature.description}</p>
                <ul className="space-y-2">
                  {feature.benefits.map((benefit, benefitIndex) => (
                    <li key={benefitIndex} className="flex items-center text-gray-700">
                      <svg className="w-5 h-5 text-dark-green mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      {benefit}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 bg-dark-green text-white">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-center mb-12"
          >
            Benefits of Professional Outdoor Kitchen Installation
          </motion.h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-dark-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-1a1 1 0 011-1h2a1 1 0 011 1v1a1 1 0 001 1m-6 0h6" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold mb-3">Increased Home Value</h3>
              <p className="text-gray-200">
                Professional outdoor kitchens can increase your property value by 15-20% while providing
                immediate lifestyle benefits and entertainment value.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-dark-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold mb-3">Enhanced Entertainment</h3>
              <p className="text-gray-200">
                Create the perfect setting for hosting gatherings, from intimate family dinners
                to large parties, with a fully functional outdoor cooking space.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-dark-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold mb-3">Year-Round Use</h3>
              <p className="text-gray-200">
                With proper design and heating elements, enjoy your outdoor kitchen through
                Boston's seasons, extending your outdoor living time significantly.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Our Outdoor Kitchen Installations
          </motion.h2>

          <Gallery images={galleryImages} />
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-8"
            >
              Ready to Create Your Dream Outdoor Kitchen?
            </motion.h2>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-xl text-center mb-12 text-gray-700"
            >
              Contact us today for a free consultation and custom design for your outdoor kitchen installation project.
            </motion.p>

            <ContactForm />
          </div>
        </div>
      </section>

      {/* Local SEO Section */}
      <section className="py-8 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <p className="text-sm text-gray-600">
              <strong>American Elm Landscape</strong> - Professional outdoor kitchen installation serving Boston,
              Cambridge, Somerville, Brookline, Back Bay, South End, and surrounding areas. Licensed and insured
              contractors specializing in custom outdoor cooking spaces, premium appliances, and weather-resistant
              outdoor kitchen components for residential properties.
            </p>
          </div>
        </div>
      </section>
    </>
  );
};

export default OutdoorKitchenInstallation;
