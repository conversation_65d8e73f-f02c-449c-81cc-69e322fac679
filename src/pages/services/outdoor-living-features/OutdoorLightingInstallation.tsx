import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { Flashlight, Lightbulb, Lamp, Lock } from 'lucide-react';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Gallery from '../../../components/ui/Gallery';
import ContactForm from '../../../components/forms/ContactForm';
import Button from '../../../components/ui/Button';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { services } from '../../../utils/constants';
import { landscapeImages } from '../../../utils/images';

const OutdoorLightingInstallation: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Outdoor Living Features', url: 'https://americanelmlandscape.com/outdoor-living-features' },
    { name: 'Outdoor Lighting Installation Boston', url: 'https://americanelmlandscape.com/outdoor-living-features/outdoor-lighting-installation' }
  ];

  const lightingTypes = [
    { title: 'Path & Walkway Lighting', description: 'Safe, attractive illumination for pathways and walkways', icon: Flashlight },
    { title: 'Accent & Uplighting', description: 'Dramatic lighting to highlight trees, architecture, and features', icon: Lightbulb },
    { title: 'Patio & Deck Lighting', description: 'Functional and ambient lighting for outdoor living areas', icon: Lamp },
    { title: 'Security Lighting', description: 'Motion-activated and strategic lighting for safety and security', icon: Lock }
  ];

  const benefits = ['Extends outdoor use after dark', 'Enhances safety and security', 'Highlights landscape features', 'Increases property value', 'Creates ambiance', 'Energy-efficient LED options', 'Low-voltage systems', 'Smart home integration'];
  const galleryImages = [
    { src: landscapeImages.gallery.pathLighting, alt: 'Outdoor lighting installation Boston', caption: 'Path lighting in Cambridge' },
    { src: landscapeImages.gallery.uplighting, alt: 'Landscape uplighting', caption: 'Tree and architectural uplighting' },
    { src: landscapeImages.gallery.patioLights, alt: 'Patio lighting', caption: 'Ambient patio lighting' },
    { src: landscapeImages.gallery.securityLights, alt: 'Security lighting', caption: 'Motion-activated security lighting' }
  ];

  const relatedServices = services.outdoorLiving.filter(service => service.name !== 'Outdoor Lighting Installation').slice(0, 3);

  return (
    <>
      <SEO
        title="Outdoor Lighting Installation Boston | Landscape & Security Lighting | American Elm Landscape"
        description="Professional outdoor lighting installation in Boston. Path lighting, uplighting, patio lighting, and security lighting. Expert design and installation."
        keywords="outdoor lighting installation Boston, landscape lighting, path lighting, security lighting, LED outdoor lights, low voltage lighting"
        canonical="https://americanelmlandscape.com/outdoor-living-features/outdoor-lighting-installation"
        schema={[
          createServiceSchema('Outdoor Lighting Installation Boston', 'Professional outdoor lighting installation services for Boston properties, enhancing beauty, safety, and security.', 'Boston'),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      <Hero title="Outdoor Lighting Installation Boston" subtitle="Illuminate Your Landscape with Professional Lighting Design" backgroundImage={landscapeImages.hero.outdoorLighting} ctaText="Light Up Your Space" ctaLink="/contact" height="medium" />

      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <Link to="/outdoor-living-features" className="text-dark-green hover:text-light-green">Outdoor Living Features</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Outdoor Lighting Installation Boston</span>
          </nav>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h1 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-4xl md:text-5xl font-serif font-bold text-dark-green mb-8">
              Outdoor Lighting Installation Boston
            </motion.h1>
            <motion.div initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: 0.2 }} className="prose prose-lg max-w-none text-gray-700 mb-12">
              <p className="text-xl leading-relaxed mb-6">
                Transform your Boston landscape with professional outdoor lighting installation. Our expertly designed lighting systems enhance safety, highlight your property's best features, and extend your outdoor living hours while adding beauty, security, and value to your home.
              </p>
              <h2 className="text-2xl font-serif font-semibold text-dark-green mb-4">Expert Lighting Design for Boston Properties</h2>
              <p className="leading-relaxed mb-6">
                Outdoor lighting is both an art and a science. We design custom lighting systems that balance aesthetics, functionality, and energy efficiency. From subtle path lighting to dramatic uplighting of trees and architecture, our installations create beautiful nighttime landscapes while improving safety and security. We use energy-efficient LED fixtures and low-voltage systems for reliable, cost-effective performance.
              </p>
              <p className="leading-relaxed">
                Our installation includes professional design, proper wiring and transformer sizing, strategic fixture placement, and integration with smart home systems when desired. We handle all electrical work to code, ensuring safe, reliable operation for years to come.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12">
            Lighting System Options
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {lightingTypes.map((type, index) => {
              const IconComponent = type.icon;
              return (
                <motion.div key={index} initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="text-center bg-white p-6 rounded-lg shadow-lg">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-dark-green/10 rounded-full mb-4">
                    <IconComponent className="w-8 h-8 text-dark-green" />
                  </div>
                  <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">{type.title}</h3>
                  <p className="text-gray-700">{type.description}</p>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-8">
              Benefits of Outdoor Lighting
            </motion.h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {benefits.map((benefit, index) => (
                <motion.div key={index} initial={{ opacity: 0, x: -20 }} whileInView={{ opacity: 1, x: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="flex items-start">
                  <svg className="w-6 h-6 text-dark-green mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-lg text-gray-700">{benefit}</span>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12">
            Outdoor Lighting Portfolio
          </motion.h2>
          <Gallery images={galleryImages} />
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl font-serif font-bold text-dark-green text-center mb-12">
            Related Outdoor Living Services
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {relatedServices.map((service, index) => (
              <motion.div key={service.path} initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="bg-white rounded-lg shadow-lg p-6 text-center">
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">{service.name} Boston</h3>
                <p className="text-gray-700 mb-4">{service.description}</p>
                <Button to={service.path} variant="outline" size="sm">Learn More</Button>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6">Ready to Illuminate Your Landscape?</h2>
              <p className="text-lg text-gray-700">Contact us for a free consultation and custom lighting design for your Boston property.</p>
            </motion.div>
            <ContactForm prefilledService="Outdoor Lighting Installation" />
          </div>
        </div>
      </section>
    </>
  );
};

export default OutdoorLightingInstallation;
