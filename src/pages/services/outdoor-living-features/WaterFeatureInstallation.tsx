import { motion } from 'framer-motion';
import { <PERSON> } from 'react-router-dom';
import { Droplets, Waves, Building2 } from 'lucide-react';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Gallery from '../../../components/ui/Gallery';
import ContactForm from '../../../components/forms/ContactForm';
import Button from '../../../components/ui/Button';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { services } from '../../../utils/constants';
import { landscapeImages } from '../../../utils/images';

const WaterFeatureInstallation: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Outdoor Living Features', url: 'https://americanelmlandscape.com/outdoor-living-features' },
    { name: 'Water Feature Installation Boston', url: 'https://americanelmlandscape.com/outdoor-living-features/water-feature-installation' }
  ];

  const waterFeatureTypes = [
    { title: 'Custom Fountains', description: 'Elegant fountains from classical to contemporary designs', icon: Droplets },
    { title: 'Waterfalls & Streams', description: 'Natural-looking waterfalls and flowing streams', icon: Waves },
    { title: 'Ponds & Pools', description: 'Decorative ponds and reflecting pools', icon: Waves },
    { title: 'Wall Fountains', description: 'Space-saving wall-mounted water features', icon: Building2 }
  ];

  const benefits = ['Soothing ambient sound', 'Attracts birds and wildlife', 'Increases property value', 'Creates focal point', 'Masks urban noise', 'Enhances ambiance', 'Year-round beauty', 'Low maintenance options'];
  const galleryImages = [
    { src: landscapeImages.gallery.waterFeature, alt: 'Water feature installation Boston', caption: 'Custom fountain in Back Bay' },
    { src: landscapeImages.gallery.fountain, alt: 'Garden fountain', caption: 'Elegant garden fountain' },
    { src: landscapeImages.gallery.waterfall, alt: 'Backyard waterfall', caption: 'Natural stone waterfall' },
    { src: landscapeImages.gallery.pond, alt: 'Decorative pond', caption: 'Decorative pond with plantings' }
  ];

  const relatedServices = services.outdoorLiving.filter(service => service.name !== 'Water Feature Installation').slice(0, 3);

  return (
    <>
      <SEO
        title="Water Feature Installation Boston | Custom Fountains & Waterfalls | American Elm Landscape"
        description="Professional water feature installation in Boston. Custom fountains, waterfalls, ponds, and streams. Expert design and installation for beautiful outdoor water elements."
        keywords="water feature installation Boston, fountain installation, waterfall installation, pond installation, outdoor water features, garden fountains"
        canonical="https://americanelmlandscape.com/outdoor-living-features/water-feature-installation"
        schema={[
          createServiceSchema('Water Feature Installation Boston', 'Professional water feature installation services for Boston properties, creating custom fountains, waterfalls, and ponds.', 'Boston'),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      <Hero title="Water Feature Installation Boston" subtitle="Custom Fountains, Waterfalls & Water Elements" backgroundImage={landscapeImages.hero.waterFeature} ctaText="Design Your Water Feature" ctaLink="/contact" height="medium" />

      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <Link to="/outdoor-living-features" className="text-dark-green hover:text-light-green">Outdoor Living Features</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Water Feature Installation Boston</span>
          </nav>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h1 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-4xl md:text-5xl font-serif font-bold text-dark-green mb-8">
              Water Feature Installation Boston
            </motion.h1>
            <motion.div initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: 0.2 }} className="prose prose-lg max-w-none text-gray-700 mb-12">
              <p className="text-xl leading-relaxed mb-6">
                Add the soothing sound and visual beauty of water to your Boston landscape with professional water feature installation. From elegant fountains to natural waterfalls, we design and build custom water elements that create tranquil focal points and enhance your outdoor living experience.
              </p>
              <h2 className="text-2xl font-serif font-semibold text-dark-green mb-4">Custom Water Features for Boston Properties</h2>
              <p className="leading-relaxed mb-6">
                Water features bring life, movement, and tranquility to outdoor spaces. We specialize in creating custom water elements that complement your landscape style and property—from formal fountains in urban courtyards to natural waterfalls in suburban gardens. Our designs incorporate proper circulation, filtration, and winterization for year-round beauty with minimal maintenance.
              </p>
              <p className="leading-relaxed">
                We handle all aspects of water feature installation—design, excavation, plumbing, electrical work, and professional finishing. Our team ensures proper drainage, circulation, and integration with your existing landscape for a seamless, beautiful result that performs reliably for years to come.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12">
            Water Feature Options
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {waterFeatureTypes.map((type, index) => {
              const IconComponent = type.icon;
              return (
                <motion.div key={index} initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="text-center bg-white p-6 rounded-lg shadow-lg">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-dark-green/10 rounded-full mb-4">
                    <IconComponent className="w-8 h-8 text-dark-green" />
                  </div>
                  <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">{type.title}</h3>
                  <p className="text-gray-700">{type.description}</p>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-8">
              Benefits of Water Features
            </motion.h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {benefits.map((benefit, index) => (
                <motion.div key={index} initial={{ opacity: 0, x: -20 }} whileInView={{ opacity: 1, x: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="flex items-start">
                  <svg className="w-6 h-6 text-dark-green mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-lg text-gray-700">{benefit}</span>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12">
            Water Feature Portfolio
          </motion.h2>
          <Gallery images={galleryImages} />
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl font-serif font-bold text-dark-green text-center mb-12">
            Related Outdoor Living Services
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {relatedServices.map((service, index) => (
              <motion.div key={service.path} initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="bg-white rounded-lg shadow-lg p-6 text-center">
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">{service.name} Boston</h3>
                <p className="text-gray-700 mb-4">{service.description}</p>
                <Button to={service.path} variant="outline" size="sm">Learn More</Button>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6">Ready for a Water Feature?</h2>
              <p className="text-lg text-gray-700">Contact us for a free consultation and custom design for your water feature installation.</p>
            </motion.div>
            <ContactForm prefilledService="Water Feature Installation" />
          </div>
        </div>
      </section>
    </>
  );
};

export default WaterFeatureInstallation;
