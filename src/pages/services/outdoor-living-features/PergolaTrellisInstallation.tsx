import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { Building2, Leaf, Flower2, Sun } from 'lucide-react';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Gallery from '../../../components/ui/Gallery';
import ContactForm from '../../../components/forms/ContactForm';
import Button from '../../../components/ui/Button';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { services } from '../../../utils/constants';
import { landscapeImages } from '../../../utils/images';

const PergolaTrellisInstallation: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Outdoor Living Features', url: 'https://americanelmlandscape.com/outdoor-living-features' },
    { name: 'Pergola & Trellis Installation Boston', url: 'https://americanelmlandscape.com/outdoor-living-features/pergola-trellis-installation' }
  ];

  const features = [
    { title: 'Custom Pergolas', description: 'Freestanding or attached pergolas in wood, vinyl, or aluminum', icon: Building2 },
    { title: 'Garden Trellises', description: 'Decorative trellises for climbing plants and privacy screening', icon: Leaf },
    { title: 'Arbors & Archways', description: 'Elegant garden arbors and entrance archways', icon: Flower2 },
    { title: 'Shade Structures', description: 'Functional shade solutions for patios and outdoor living areas', icon: Sun }
  ];

  const benefits = ['Defines outdoor spaces', 'Provides partial shade', 'Supports climbing plants', 'Adds architectural interest', 'Increases property value', 'Creates privacy', 'Extends living space', 'Low maintenance options'];
  const galleryImages = [
    { src: landscapeImages.gallery.pergola, alt: 'Custom pergola installation Boston', caption: 'Cedar pergola over patio' },
    { src: landscapeImages.gallery.trellis, alt: 'Garden trellis', caption: 'Decorative garden trellis' },
    { src: landscapeImages.gallery.arbor, alt: 'Garden arbor', caption: 'Entrance arbor with climbing roses' },
    { src: landscapeImages.gallery.shadeStructure, alt: 'Outdoor shade structure', caption: 'Modern shade pergola' }
  ];

  const relatedServices = services.outdoorLiving.filter(service => service.name !== 'Pergola & Trellis Installation').slice(0, 3);

  return (
    <>
      <SEO
        title="Pergola & Trellis Installation Boston | Custom Outdoor Structures | American Elm Landscape"
        description="Professional pergola and trellis installation in Boston. Custom outdoor structures including pergolas, arbors, and trellises. Expert design and installation."
        keywords="pergola installation Boston, trellis installation, arbor installation, outdoor structures, garden pergola, custom pergola"
        canonical="https://americanelmlandscape.com/outdoor-living-features/pergola-trellis-installation"
        schema={[
          createServiceSchema('Pergola & Trellis Installation Boston', 'Professional pergola and trellis installation services for Boston properties, creating beautiful outdoor structures.', 'Boston'),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      <Hero title="Pergola & Trellis Installation Boston" subtitle="Custom Outdoor Structures for Shade & Beauty" backgroundImage={landscapeImages.hero.pergola} ctaText="Design Your Structure" ctaLink="/contact" height="medium" />

      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <Link to="/outdoor-living-features" className="text-dark-green hover:text-light-green">Outdoor Living Features</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Pergola & Trellis Installation Boston</span>
          </nav>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h1 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-4xl md:text-5xl font-serif font-bold text-dark-green mb-8">
              Pergola & Trellis Installation Boston
            </motion.h1>
            <motion.div initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: 0.2 }} className="prose prose-lg max-w-none text-gray-700 mb-12">
              <p className="text-xl leading-relaxed mb-6">
                Enhance your Boston outdoor space with custom pergola and trellis installation. Our beautiful structures provide shade, define outdoor rooms, support climbing plants, and add architectural interest to your landscape while creating inviting spaces for relaxation and entertaining.
              </p>
              <h2 className="text-2xl font-serif font-semibold text-dark-green mb-4">Custom Outdoor Structures for Boston Properties</h2>
              <p className="leading-relaxed mb-6">
                Pergolas and trellises transform outdoor spaces by adding vertical elements, creating partial shade, and providing support for beautiful climbing plants. We design and build custom structures that complement your home's architecture and landscape style—from traditional cedar pergolas to modern aluminum designs. Our installations are built to withstand Boston's weather while providing years of beauty and functionality.
              </p>
              <p className="leading-relaxed">
                We handle complete installation including site preparation, foundation work, structural construction, and finishing details. Our team ensures proper engineering, quality materials, and professional craftsmanship for structures that enhance your property's value and your outdoor living experience.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12">
            Structure Options
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <motion.div key={index} initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="text-center bg-white p-6 rounded-lg shadow-lg">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-dark-green/10 rounded-full mb-4">
                    <IconComponent className="w-8 h-8 text-dark-green" />
                  </div>
                  <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">{feature.title}</h3>
                  <p className="text-gray-700">{feature.description}</p>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-8">
              Benefits of Pergolas & Trellises
            </motion.h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {benefits.map((benefit, index) => (
                <motion.div key={index} initial={{ opacity: 0, x: -20 }} whileInView={{ opacity: 1, x: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="flex items-start">
                  <svg className="w-6 h-6 text-dark-green mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-lg text-gray-700">{benefit}</span>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12">
            Pergola & Trellis Portfolio
          </motion.h2>
          <Gallery images={galleryImages} />
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl font-serif font-bold text-dark-green text-center mb-12">
            Related Outdoor Living Services
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {relatedServices.map((service, index) => (
              <motion.div key={service.path} initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="bg-white rounded-lg shadow-lg p-6 text-center">
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">{service.name} Boston</h3>
                <p className="text-gray-700 mb-4">{service.description}</p>
                <Button to={service.path} variant="outline" size="sm">Learn More</Button>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6">Ready for Your Pergola or Trellis?</h2>
              <p className="text-lg text-gray-700">Contact us for a free consultation and custom design for your outdoor structure.</p>
            </motion.div>
            <ContactForm prefilledService="Pergola & Trellis Installation" />
          </div>
        </div>
      </section>
    </>
  );
};

export default PergolaTrellisInstallation;
