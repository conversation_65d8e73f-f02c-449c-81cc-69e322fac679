import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Card from '../../../components/ui/Card';
import ContactForm from '../../../components/forms/ContactForm';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { landscapeImages } from '../../../utils/images';

const LandscapeDesignerArlington: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Locations', url: 'https://americanelmlandscape.com/locations' },
    { name: 'Landscape Designer Arlington', url: 'https://americanelmlandscape.com/locations/arlington/landscape-designer' }
  ];

  const arlingtonServices = [
    {
      name: 'Arlington Center Landscaping',
      description: 'Beautiful landscape design for Arlington Center properties, combining suburban charm with modern functionality.',
      link: '/services/urban-landscape-design/suburban-landscape-design'
    },
    {
      name: 'Family-Friendly Yard Design',
      description: 'Safe, functional outdoor spaces perfect for Arlington families with children and pets.',
      link: '/services/urban-landscape-design/family-friendly-landscaping'
    },
    {
      name: 'Synthetic Turf for Active Families',
      description: 'Durable, safe artificial grass perfect for Arlington\'s active families and year-round play.',
      link: '/services/synthetic-turf-installation/family-friendly-synthetic-turf'
    },
    {
      name: 'Backyard Patio Installation',
      description: 'Custom patio design for Arlington homes, perfect for family gatherings and outdoor entertaining.',
      link: '/services/custom-hardscaping-patios/family-patio-design'
    },
    {
      name: 'Playground & Recreation Areas',
      description: 'Safe, fun outdoor play areas designed specifically for Arlington families.',
      link: '/services/outdoor-living-features/playground-installation'
    },
    {
      name: 'Seasonal Garden Design',
      description: 'Four-season garden design that provides year-round beauty in Arlington\'s climate.',
      link: '/services/urban-landscape-design/seasonal-garden-design'
    }
  ];

  return (
    <>
      <SEO
        title="Landscape Designer Arlington | Family-Friendly Outdoor Spaces | American Elm Landscape"
        description="Expert landscape design for Arlington, MA families. Specializing in safe, functional outdoor spaces, synthetic turf, and family-friendly landscaping."
        keywords="landscape designer Arlington, Arlington landscaping, family-friendly landscaping, Arlington outdoor spaces"
        canonical="https://americanelmlandscape.com/locations/arlington/landscape-designer"
        schema={[
          createServiceSchema(
            'Landscape Designer Arlington',
            'Professional landscape design services for Arlington, MA families, specializing in safe, functional outdoor spaces.',
            'Arlington, MA'
          ),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      <Hero
        title="Landscape Designer Arlington"
        subtitle="Family-Friendly Outdoor Spaces for Arlington Homes"
        backgroundImage={landscapeImages.hero.default}
        ctaText="Schedule Consultation"
        ctaLink="/contact"
        height="medium"
      />

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-serif font-bold text-dark-green mb-8"
            >
              Arlington's Family-Focused Landscape Experts
            </motion.h1>
            
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-lg text-gray-600 leading-relaxed"
            >
              Arlington families deserve outdoor spaces that are both beautiful and functional. 
              We specialize in creating safe, durable landscapes that enhance family life while 
              adding value to your Arlington home.
            </motion.p>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Arlington Landscape Design Services
          </motion.h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {arlingtonServices.map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card
                  title={service.name}
                  description={service.description}
                  link={service.link}
                  image={landscapeImages.gallery.turf}
                />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-4">
                Ready to Create Your Arlington Family Oasis?
              </h2>
            </motion.div>
            <ContactForm />
          </div>
        </div>
      </section>
    </>
  );
};

export default LandscapeDesignerArlington;
