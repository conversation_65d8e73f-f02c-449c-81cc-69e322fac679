import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Card from '../../../components/ui/Card';
import ContactForm from '../../../components/forms/ContactForm';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { landscapeImages } from '../../../utils/images';

const LandscapeDesignerSomerville: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Locations', url: 'https://americanelmlandscape.com/locations' },
    { name: 'Landscape Designer Somerville', url: 'https://americanelmlandscape.com/locations/somerville/landscape-designer' }
  ];

  const somervilleServices = [
    {
      name: 'Davis Square Area Landscaping',
      description: 'Creative landscape solutions for Davis Square\'s eclectic neighborhood, blending artistic flair with practical urban design.',
      link: '/services/urban-landscape-design/urban-backyard-transformation'
    },
    {
      name: 'Triple Decker Property Design',
      description: 'Specialized landscaping for Somerville\'s iconic triple-decker homes, maximizing small yards and shared spaces.',
      link: '/services/urban-landscape-design/small-yard-landscape-design-build'
    },
    {
      name: 'Assembly Row Modern Landscapes',
      description: 'Contemporary landscape design for Assembly Row\'s modern developments and waterfront properties.',
      link: '/services/urban-landscape-design/modern-landscape-design'
    },
    {
      name: 'Pet-Friendly Synthetic Turf',
      description: 'Durable, low-maintenance artificial grass perfect for Somerville\'s dog-loving community.',
      link: '/services/synthetic-turf-installation/pet-friendly-artificial-turf'
    },
    {
      name: 'Small Space Patios',
      description: 'Custom patio design that maximizes functionality in Somerville\'s compact outdoor spaces.',
      link: '/services/custom-hardscaping-patios/custom-patio-installation'
    },
    {
      name: 'Urban Fire Pit Installation',
      description: 'Cozy fire pit installations perfect for Somerville\'s community-oriented neighborhoods.',
      link: '/services/custom-hardscaping-patios/custom-fire-pits'
    }
  ];

  const nearbyAreas = [
    { name: 'Cambridge', path: '/locations/cambridge/landscape-designer' },
    { name: 'Arlington', path: '/locations/arlington/landscape-designer' },
    { name: 'Medford', path: '/locations/medford/landscape-designer' },
    { name: 'Boston', path: '/locations/boston/landscape-designer-back-bay' }
  ];

  return (
    <>
      <SEO
        title="Landscape Designer Somerville | Davis Square & Assembly Row Specialists | American Elm Landscape"
        description="Expert landscape design for Somerville, MA properties. Specializing in triple-decker homes, Davis Square area, and Assembly Row modern developments."
        keywords="landscape designer Somerville, Somerville landscaping, Davis Square landscaping, Assembly Row landscape design, triple decker landscaping, urban garden design Somerville"
        canonical="https://americanelmlandscape.com/locations/somerville/landscape-designer"
        schema={[
          createServiceSchema(
            'Landscape Designer Somerville',
            'Professional landscape design services for Somerville, MA properties, including Davis Square, Assembly Row, and triple-decker homes.',
            'Somerville, MA'
          ),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      {/* Hero Section */}
      <Hero
        title="Landscape Designer Somerville"
        subtitle="Creative Urban Design for Somerville's Unique Character"
        backgroundImage={landscapeImages.hero.smallYard}
        ctaText="Schedule Consultation"
        ctaLink="/contact"
        height="medium"
      />

      {/* Breadcrumbs */}
      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-500">Locations</span>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Landscape Designer Somerville</span>
          </nav>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-serif font-bold text-dark-green mb-8"
            >
              Somerville's Creative Landscape Design Experts
            </motion.h1>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="prose prose-lg max-w-none"
            >
              <p className="text-lg text-gray-600 leading-relaxed mb-6">
                Somerville's vibrant, artistic community deserves landscape design that reflects its
                creative spirit and urban innovation. American Elm Landscape specializes in transforming
                Somerville's unique outdoor spaces - from triple-decker backyards to modern Assembly Row
                developments - into beautiful, functional landscapes that enhance the city's distinctive character.
              </p>

              <p className="text-lg text-gray-600 leading-relaxed mb-6">
                Known for its diverse neighborhoods, from the eclectic Davis Square to the waterfront
                Assembly Row, Somerville presents exciting opportunities for creative landscape design.
                We understand the challenges of urban density while embracing the community's commitment
                to sustainability and innovation.
              </p>

              <p className="text-lg text-gray-600 leading-relaxed">
                Whether you're looking to maximize a small triple-decker yard, create a modern outdoor
                space in Assembly Row, or design a community garden that brings neighbors together,
                we create landscapes that celebrate Somerville's unique urban culture.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Neighborhoods Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Somerville Neighborhoods We Serve
          </motion.h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-white p-6 rounded-lg shadow-md"
            >
              <h3 className="text-xl font-serif font-bold text-dark-green mb-3">Davis Square</h3>
              <p className="text-gray-600">
                Creative landscape solutions for this eclectic, artistic neighborhood with its
                unique character and community spirit.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="bg-white p-6 rounded-lg shadow-md"
            >
              <h3 className="text-xl font-serif font-bold text-dark-green mb-3">Assembly Row</h3>
              <p className="text-gray-600">
                Modern landscape design for waterfront developments and contemporary
                residential properties in this growing district.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-white p-6 rounded-lg shadow-md"
            >
              <h3 className="text-xl font-serif font-bold text-dark-green mb-3">Union Square</h3>
              <p className="text-gray-600">
                Innovative urban landscaping for this transit-oriented development
                with sustainable design principles.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="bg-white p-6 rounded-lg shadow-md"
            >
              <h3 className="text-xl font-serif font-bold text-dark-green mb-3">Porter Square</h3>
              <p className="text-gray-600">
                Thoughtful landscape design for this diverse neighborhood
                connecting Somerville to Cambridge.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="bg-white p-6 rounded-lg shadow-md"
            >
              <h3 className="text-xl font-serif font-bold text-dark-green mb-3">Ball Square</h3>
              <p className="text-gray-600">
                Community-focused landscape solutions for this family-friendly
                neighborhood with its strong local identity.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="bg-white p-6 rounded-lg shadow-md"
            >
              <h3 className="text-xl font-serif font-bold text-dark-green mb-3">Winter Hill</h3>
              <p className="text-gray-600">
                Practical landscape design for this residential area with
                focus on maximizing small urban spaces.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Specialized Services for Somerville Properties
          </motion.h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {somervilleServices.map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card
                  title={service.name}
                  description={service.description}
                  link={service.link}
                  linkText="Learn More"
                />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-16 bg-dark-green text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-3xl md:text-4xl font-serif font-bold mb-8"
            >
              Why Somerville Chooses American Elm Landscape
            </motion.h2>

            <div className="grid md:grid-cols-3 gap-8 mt-12">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-dark-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a4 4 0 004-4V5z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-3">Creative Innovation</h3>
                <p className="text-gray-300">
                  We embrace Somerville's artistic spirit with innovative landscape designs
                  that reflect the city's creative energy and community values.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-dark-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-3">Triple-Decker Specialists</h3>
                <p className="text-gray-300">
                  Expert knowledge of Somerville's iconic triple-decker properties
                  and how to maximize their unique outdoor spaces.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-dark-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-3">Community Focus</h3>
                <p className="text-gray-300">
                  Understanding of Somerville's strong community bonds and how
                  landscape design can enhance neighborhood connections.
                </p>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Nearby Areas Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl font-serif font-bold text-dark-green text-center mb-8"
          >
            We Also Serve Nearby Areas
          </motion.h2>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex flex-wrap justify-center gap-4"
          >
            {nearbyAreas.map((area, index) => (
              <Link
                key={index}
                to={area.path}
                className="bg-white px-6 py-3 rounded-lg shadow-md hover:shadow-lg transition-shadow text-dark-green hover:text-light-green font-medium"
              >
                {area.name}
              </Link>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-dark-green text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-3xl md:text-4xl font-serif font-bold text-center mb-8"
            >
              Ready to Transform Your Somerville Property?
            </motion.h2>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-xl text-center mb-12"
            >
              Let's create a landscape that celebrates Somerville's creative spirit and enhances your outdoor living experience.
            </motion.p>

            <ContactForm />
          </div>
        </div>
      </section>

      {/* Local SEO Section */}
      <section className="py-8 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <p className="text-sm text-gray-600">
              <strong>American Elm Landscape</strong> - Professional landscape design and installation serving
              Somerville, MA including Davis Square, Assembly Row, Union Square, Porter Square, Ball Square,
              and Winter Hill. Licensed and insured landscape contractors specializing in urban design,
              triple-decker properties, sustainable landscaping, and modern outdoor living spaces.
            </p>
          </div>
        </div>
      </section>
    </>
  );
};

export default LandscapeDesignerSomerville;