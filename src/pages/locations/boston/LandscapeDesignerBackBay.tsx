import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Card from '../../../components/ui/Card';
import ContactForm from '../../../components/forms/ContactForm';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { services } from '../../../utils/constants';

const LandscapeDesignerBackBay: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Locations', url: 'https://americanelmlandscape.com/locations' },
    { name: 'Landscape Designer Back Bay Boston', url: 'https://americanelmlandscape.com/locations/boston/landscape-designer-back-bay' }
  ];

  const backBayServices = [
    {
      name: 'Victorian Brownstone Landscaping',
      description: 'Specialized design for Back Bay\'s historic Victorian brownstones, respecting architectural character while creating modern outdoor living.',
      link: '/urban-landscape-design/high-end-landscape-design'
    },
    {
      name: 'Small Courtyard Design',
      description: 'Maximize your limited Back Bay outdoor space with creative design solutions for courtyards and small yards.',
      link: '/urban-landscape-design/small-yard-landscape-design-build'
    },
    {
      name: 'Rooftop Garden Installation',
      description: 'Transform Back Bay rooftops into private garden retreats with proper drainage and wind-resistant plantings.',
      link: '/urban-landscape-design/modern-landscape-design'
    },
    {
      name: 'Custom Patio Installation',
      description: 'Professional patio installation designed for Back Bay\'s narrow lots and historic building requirements.',
      link: '/custom-hardscaping-patios/custom-patio-installation'
    },
    {
      name: 'Synthetic Turf for Urban Yards',
      description: 'Low-maintenance artificial grass perfect for Back Bay\'s small yards and high-traffic areas.',
      link: '/synthetic-turf-installation/artificial-grass-small-yards'
    }
  ];

  const nearbyAreas = [
    { name: 'South End', path: '/locations/boston/landscape-designer-south-end' },
    { name: 'Cambridge', path: '/locations/cambridge/landscape-designer' },
    { name: 'Beacon Hill', path: '/locations/boston/landscape-designer-beacon-hill' },
    { name: 'North End', path: '/locations/boston/landscape-designer-north-end' }
  ];

  return (
    <>
      <SEO
        title="Landscape Designer Back Bay Boston | Victorian Brownstone Specialists | American Elm Landscape"
        description="Expert landscape design for Back Bay Boston properties. Specializing in Victorian brownstones, small courtyards, and urban gardens. Free consultation available."
        keywords="landscape designer Back Bay Boston, Back Bay landscaping, Victorian brownstone landscaping, small yard design Back Bay, urban garden design Boston"
        canonical="https://americanelmlandscape.com/locations/boston/landscape-designer-back-bay"
        schema={[
          createServiceSchema(
            'Landscape Designer Back Bay Boston',
            'Professional landscape design services specifically for Back Bay Boston properties, including Victorian brownstones and small urban spaces.',
            'Back Bay, Boston'
          ),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      {/* Hero Section */}
      <Hero
        title="Landscape Designer Back Bay Boston"
        subtitle="Victorian Elegance Meets Modern Outdoor Living"
        backgroundImage="/images/hero-back-bay-landscape.jpg"
        ctaText="Schedule Consultation"
        ctaLink="/contact"
        height="medium"
      />

      {/* Breadcrumbs */}
      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-500">Locations</span>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Landscape Designer Back Bay Boston</span>
          </nav>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-serif font-bold text-dark-green mb-8"
            >
              Back Bay's Premier Landscape Design Specialists
            </motion.h1>
            
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="prose prose-lg max-w-none text-gray-700 mb-12"
            >
              <p className="text-xl leading-relaxed mb-6">
                Back Bay's Victorian brownstones and historic architecture demand specialized landscape design expertise. 
                American Elm Landscape understands the unique challenges and opportunities of designing outdoor spaces 
                in one of Boston's most prestigious neighborhoods.
              </p>
              
              <p className="leading-relaxed mb-6">
                From the narrow lots along Commonwealth Avenue to the charming courtyards behind Marlborough Street 
                brownstones, we create sophisticated outdoor environments that complement Back Bay's architectural heritage 
                while meeting modern lifestyle needs. Our designs respect historic district guidelines while maximizing 
                the potential of every square foot.
              </p>
              
              <h2 className="text-2xl font-serif font-semibold text-dark-green mb-4">
                Understanding Back Bay's Unique Landscape Challenges
              </h2>
              
              <p className="leading-relaxed mb-6">
                Back Bay presents distinct design challenges: compact lots, historic preservation requirements, 
                limited access for construction, and the need to create privacy in a dense urban environment. 
                Our team has extensive experience navigating these constraints while creating beautiful, functional outdoor spaces.
              </p>
              
              <p className="leading-relaxed">
                We understand Back Bay's soil conditions, drainage patterns, and microclimates. Our plant selections 
                thrive in the neighborhood's urban environment while providing year-round interest that complements 
                the area's Victorian elegance.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Our Back Bay Landscape Services
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {backBayServices.slice(0, 3).map((service, index) => (
              <motion.div
                key={service.name}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card
                  title={service.name}
                  description={service.description}
                  image={`/images/${service.name.toLowerCase().replace(/\s+/g, '-')}.jpg`}
                  imageAlt={`${service.name} in Back Bay Boston`}
                  link={service.link}
                  ctaText="Learn More"
                />
              </motion.div>
            ))}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {backBayServices.slice(3).map((service, index) => (
              <motion.div
                key={service.name}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-lg shadow-lg p-6"
              >
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">
                  {service.name}
                </h3>
                <p className="text-gray-700 mb-4">{service.description}</p>
                <Link
                  to={service.link}
                  className="inline-flex items-center text-dark-green font-semibold hover:text-light-green transition-colors"
                >
                  Learn More
                  <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Local Expertise Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-3xl font-serif font-bold text-dark-green mb-6">
                Why Choose Us for Your Back Bay Project?
              </h2>
              
              <div className="space-y-6">
                <div className="flex items-start">
                  <div className="w-12 h-12 bg-dark-green text-white rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                    🏛️
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Historic District Expertise</h3>
                    <p className="text-gray-700">Deep understanding of Back Bay's historic preservation requirements and architectural guidelines.</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="w-12 h-12 bg-dark-green text-white rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                    📏
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Small Space Specialists</h3>
                    <p className="text-gray-700">Creative solutions for maximizing Back Bay's compact lots and challenging access situations.</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="w-12 h-12 bg-dark-green text-white rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                    🌿
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Urban Plant Expertise</h3>
                    <p className="text-gray-700">Specialized knowledge of plants that thrive in Back Bay's urban microclimate and soil conditions.</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="w-12 h-12 bg-dark-green text-white rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                    🔧
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Logistical Excellence</h3>
                    <p className="text-gray-700">Experienced in managing construction logistics in Back Bay's dense, permit-sensitive environment.</p>
                  </div>
                </div>
              </div>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-gray-50 rounded-lg p-8"
            >
              <h3 className="text-2xl font-serif font-semibold text-dark-green mb-6">
                Back Bay Project Highlights
              </h3>
              
              <div className="space-y-4 text-gray-700">
                <div className="border-l-4 border-dark-green pl-4">
                  <h4 className="font-semibold">Commonwealth Avenue Brownstone</h4>
                  <p className="text-sm">Complete courtyard renovation with custom hardscaping and privacy screening</p>
                </div>
                
                <div className="border-l-4 border-dark-green pl-4">
                  <h4 className="font-semibold">Marlborough Street Garden</h4>
                  <p className="text-sm">Victorian-inspired garden design with modern irrigation and lighting</p>
                </div>
                
                <div className="border-l-4 border-dark-green pl-4">
                  <h4 className="font-semibold">Newbury Street Rooftop</h4>
                  <p className="text-sm">Wind-resistant rooftop garden with panoramic city views</p>
                </div>
                
                <div className="border-l-4 border-dark-green pl-4">
                  <h4 className="font-semibold">Berkeley Street Patio</h4>
                  <p className="text-sm">Custom stone patio with integrated seating and fire feature</p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Nearby Areas */}
      <section className="py-16 bg-dark-green text-white">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl font-serif font-bold text-center mb-12"
          >
            Serving Nearby Boston Neighborhoods
          </motion.h2>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {nearbyAreas.map((area, index) => (
              <motion.div
                key={area.name}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <Link
                  to={area.path}
                  className="block text-lg font-semibold hover:text-gray-300 transition-colors"
                >
                  {area.name} Landscape Designer
                </Link>
              </motion.div>
            ))}
          </div>
          
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-center mt-8"
          >
            <p className="text-gray-300">
              Proudly serving all of Greater Boston with specialized expertise in each neighborhood's unique character.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6">
                Ready to Transform Your Back Bay Property?
              </h2>
              <p className="text-lg text-gray-700">
                Let's discuss your Back Bay landscape project. Our team understands the neighborhood's unique 
                requirements and will create a design that enhances your property's historic character.
              </p>
            </motion.div>
            
            <ContactForm prefilledService="Back Bay Landscape Design" />
          </div>
        </div>
      </section>
    </>
  );
};

export default LandscapeDesignerBackBay;
