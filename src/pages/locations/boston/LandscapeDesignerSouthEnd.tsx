import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Gallery from '../../../components/ui/Gallery';
import ContactForm from '../../../components/forms/ContactForm';
import Button from '../../../components/ui/Button';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { services } from '../../../utils/constants';
import { landscapeImages } from '../../../utils/images';

const LandscapeDesignerSouthEnd: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Locations', url: 'https://americanelmlandscape.com/locations' },
    { name: 'Landscape Designer South End Boston', url: 'https://americanelmlandscape.com/locations/boston/landscape-designer-south-end' }
  ];

  const southEndSpecialties = [
    { title: 'Victorian Gardens', description: 'Period-appropriate plantings and design for historic South End properties', icon: '🏛️' },
    { title: 'Small Courtyards', description: 'Maximizing beauty and function in compact South End outdoor spaces', icon: '🌿' },
    { title: 'Historic Restoration', description: 'Sensitive restoration of historic landscapes and garden features', icon: '🏺' },
    { title: 'Urban Privacy', description: 'Creating private retreats in the heart of the city', icon: '🌳' }
  ];

  const southEndChallenges = [
    'Narrow lot widths and small footprints',
    'Historic preservation guidelines',
    'Limited sunlight in courtyards',
    'Privacy in dense urban setting',
    'Period-appropriate design',
    'Challenging access for materials',
    'Integration with brownstone architecture',
    'Year-round visual interest'
  ];

  const galleryImages = [
    { src: landscapeImages.gallery.southEnd, alt: 'South End landscape design', caption: 'Victorian garden in South End' },
    { src: landscapeImages.gallery.courtyard, alt: 'South End courtyard', caption: 'Private South End courtyard' },
    { src: landscapeImages.gallery.brownstone, alt: 'Brownstone landscaping', caption: 'Historic brownstone landscaping' },
    { src: landscapeImages.gallery.urbanGarden, alt: 'Urban garden South End', caption: 'Lush urban garden design' }
  ];

  const featuredServices = services.urbanDesign.slice(0, 3);

  return (
    <>
      <SEO
        title="Landscape Designer South End Boston | Victorian Garden Specialists | American Elm Landscape"
        description="Expert landscape design for South End Boston properties. Specializing in Victorian gardens, small courtyards, and historic property landscaping."
        keywords="landscape designer South End Boston, South End landscaping, Victorian garden design, historic property landscaping, brownstone landscaping"
        canonical="https://americanelmlandscape.com/locations/boston/landscape-designer-south-end"
        schema={[
          createServiceSchema('Landscape Designer South End Boston', 'Professional landscape design services for South End Boston properties, specializing in Victorian gardens and historic landscapes.', 'South End, Boston'),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      <Hero
        title="Landscape Designer South End Boston"
        subtitle="Victorian Garden Design for Boston's Historic South End"
        backgroundImage={landscapeImages.hero.southEnd}
        ctaText="Schedule Consultation"
        ctaLink="/contact"
        height="medium"
      />

      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <Link to="/locations" className="text-dark-green hover:text-light-green">Locations</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Landscape Designer South End Boston</span>
          </nav>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h1 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-4xl md:text-5xl font-serif font-bold text-dark-green mb-8">
              Landscape Designer South End Boston
            </motion.h1>
            <motion.div initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: 0.2 }} className="prose prose-lg max-w-none text-gray-700 mb-12">
              <p className="text-xl leading-relaxed mb-6">
                Transform your South End property with expert landscape design that honors the neighborhood's Victorian heritage while creating beautiful, functional outdoor spaces. We specialize in the unique challenges and opportunities of South End landscaping—from narrow courtyards to historic brownstone gardens.
              </p>
              <h2 className="text-2xl font-serif font-semibold text-dark-green mb-4">Experts in South End Landscape Design</h2>
              <p className="leading-relaxed mb-6">
                The South End's historic brownstones and Victorian architecture require specialized landscape design expertise. We understand the neighborhood's preservation guidelines, architectural styles, and unique spatial constraints. Our designs complement the area's historic character while incorporating modern functionality and low-maintenance solutions perfect for urban living.
              </p>
              <p className="leading-relaxed mb-6">
                From intimate rear courtyards to elegant front gardens, we create landscapes that enhance your property's value and beauty. We're experienced in working within the South End's narrow lots, limited access, and historic preservation requirements, delivering professional results that respect the neighborhood's character.
              </p>
              <h3 className="text-xl font-semibold text-dark-green mb-4">Victorian Garden Specialists</h3>
              <p className="leading-relaxed">
                Our team specializes in period-appropriate Victorian garden design, using traditional plantings, materials, and design principles that complement South End architecture. We create gardens that feel authentic to the neighborhood's history while incorporating modern irrigation, lighting, and maintenance solutions.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12">
            South End Specialties
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {southEndSpecialties.map((specialty, index) => (
              <motion.div key={index} initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="text-center bg-white p-6 rounded-lg shadow-lg">
                <div className="text-4xl mb-4">{specialty.icon}</div>
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">{specialty.title}</h3>
                <p className="text-gray-700">{specialty.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-8">
              South End Landscape Challenges We Solve
            </motion.h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {southEndChallenges.map((challenge, index) => (
                <motion.div key={index} initial={{ opacity: 0, x: -20 }} whileInView={{ opacity: 1, x: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="flex items-start">
                  <svg className="w-6 h-6 text-dark-green mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-lg text-gray-700">{challenge}</span>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12">
            South End Portfolio
          </motion.h2>
          <Gallery images={galleryImages} />
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2 initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-3xl font-serif font-bold text-dark-green text-center mb-12">
            Featured Services for South End
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {featuredServices.map((service, index) => (
              <motion.div key={service.path} initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6, delay: index * 0.1 }} className="bg-white rounded-lg shadow-lg p-6 text-center">
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">{service.name} Boston</h3>
                <p className="text-gray-700 mb-4">{service.description}</p>
                <Button to={service.path} variant="outline" size="sm">Learn More</Button>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6">Ready to Transform Your South End Property?</h2>
              <p className="text-lg text-gray-700">Contact us for a free consultation and discover how we can create a beautiful landscape for your South End home.</p>
            </motion.div>
            <ContactForm prefilledService="South End Landscape Design" />
          </div>
        </div>
      </section>
    </>
  );
};

export default LandscapeDesignerSouthEnd;
