import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Card from '../../../components/ui/Card';
import ContactForm from '../../../components/forms/ContactForm';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { landscapeImages } from '../../../utils/images';

const LandscapeDesignerWinchester: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Locations', url: 'https://americanelmlandscape.com/locations' },
    { name: 'Landscape Designer Winchester', url: 'https://americanelmlandscape.com/locations/winchester/landscape-designer' }
  ];

  const winchesterServices = [
    {
      name: 'Luxury Estate Landscaping',
      description: 'High-end landscape design for Winchester\'s prestigious properties, featuring custom design and premium materials.',
      link: '/services/urban-landscape-design/luxury-landscape-design'
    },
    {
      name: 'Historic Property Landscaping',
      description: 'Specialized design for Winchester\'s historic homes, preserving character while adding modern amenities.',
      link: '/services/urban-landscape-design/historic-property-landscaping'
    },
    {
      name: 'Custom Hardscaping & Patios',
      description: 'Premium hardscaping solutions designed for Winchester\'s upscale residential properties.',
      link: '/services/custom-hardscaping-patios/luxury-hardscape-design'
    },
    {
      name: 'Outdoor Kitchen & Entertainment',
      description: 'Luxury outdoor kitchens and entertainment areas perfect for Winchester\'s sophisticated lifestyle.',
      link: '/services/outdoor-living-features/luxury-outdoor-kitchen'
    },
    {
      name: 'Pool & Spa Landscaping',
      description: 'Beautiful landscape design around pools and spas, creating resort-like experiences at home.',
      link: '/services/outdoor-living-features/pool-landscape-design'
    },
    {
      name: 'Seasonal Color Programs',
      description: 'Year-round beauty with professional seasonal plantings and maintenance programs.',
      link: '/services/urban-landscape-design/seasonal-landscape-design'
    }
  ];

  return (
    <>
      <SEO
        title="Landscape Designer Winchester | Luxury Estate Specialists | American Elm Landscape"
        description="Expert landscape design for Winchester, MA luxury properties. Specializing in estate landscaping, historic properties, and premium outdoor living."
        keywords="landscape designer Winchester, Winchester landscaping, luxury landscape design, estate landscaping Winchester"
        canonical="https://americanelmlandscape.com/locations/winchester/landscape-designer"
        schema={[
          createServiceSchema(
            'Landscape Designer Winchester',
            'Professional luxury landscape design services for Winchester, MA estates and historic properties.',
            'Winchester, MA'
          ),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      <Hero
        title="Landscape Designer Winchester"
        subtitle="Luxury Estate Landscaping for Winchester's Finest Properties"
        backgroundImage={landscapeImages.hero.luxuryDesign}
        ctaText="Schedule Consultation"
        ctaLink="/contact"
        height="medium"
      />

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-serif font-bold text-dark-green mb-8"
            >
              Winchester's Premier Luxury Landscape Design
            </motion.h1>
            
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-lg text-gray-600 leading-relaxed"
            >
              Winchester's prestigious properties deserve landscape design that matches their elegance. 
              We create stunning outdoor environments that enhance your estate's beauty and value.
            </motion.p>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {winchesterServices.map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card
                  title={service.name}
                  description={service.description}
                  link={service.link}
                  image={landscapeImages.gallery.modern}
                />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-4">
                Transform Your Winchester Estate
              </h2>
            </motion.div>
            <ContactForm />
          </div>
        </div>
      </section>
    </>
  );
};

export default LandscapeDesignerWinchester;
