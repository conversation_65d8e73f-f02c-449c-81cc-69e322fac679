import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Card from '../../../components/ui/Card';
import ContactForm from '../../../components/forms/ContactForm';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { landscapeImages } from '../../../utils/images';

const LandscapeDesignerMedford: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Locations', url: 'https://americanelmlandscape.com/locations' },
    { name: 'Landscape Designer Medford', url: 'https://americanelmlandscape.com/locations/medford/landscape-designer' }
  ];

  const medfordServices = [
    {
      name: 'Medford Square Area Landscaping',
      description: 'Professional landscape design for Medford Square properties, combining urban convenience with suburban comfort.',
      link: '/services/urban-landscape-design/suburban-landscape-design'
    },
    {
      name: 'Hillside Property Design',
      description: 'Specialized landscaping for Medford\'s hilly terrain, featuring terracing and slope stabilization.',
      link: '/services/urban-landscape-design/hillside-landscape-design'
    },
    {
      name: 'Family Backyard Transformation',
      description: 'Complete backyard makeovers designed for Medford families, focusing on functionality and beauty.',
      link: '/services/urban-landscape-design/family-backyard-design'
    },
    {
      name: 'Synthetic Turf Installation',
      description: 'Low-maintenance artificial grass perfect for Medford\'s busy families and challenging soil conditions.',
      link: '/services/synthetic-turf-installation/residential-synthetic-turf'
    },
    {
      name: 'Patio & Fire Pit Installation',
      description: 'Custom patios and fire pits designed for Medford\'s community-focused neighborhoods.',
      link: '/services/custom-hardscaping-patios/patio-fire-pit-combo'
    },
    {
      name: 'Drainage Solutions',
      description: 'Professional drainage systems to address Medford\'s unique topographical challenges.',
      link: '/services/outdoor-living-features/drainage-solutions'
    }
  ];

  return (
    <>
      <SEO
        title="Landscape Designer Medford | Hillside & Family Property Specialists | American Elm Landscape"
        description="Expert landscape design for Medford, MA properties. Specializing in hillside landscaping, family backyards, and drainage solutions."
        keywords="landscape designer Medford, Medford landscaping, hillside landscaping, family landscape design Medford"
        canonical="https://americanelmlandscape.com/locations/medford/landscape-designer"
        schema={[
          createServiceSchema(
            'Landscape Designer Medford',
            'Professional landscape design services for Medford, MA properties, specializing in hillside and family-friendly designs.',
            'Medford, MA'
          ),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      <Hero
        title="Landscape Designer Medford"
        subtitle="Transforming Medford Properties with Expert Design"
        backgroundImage={landscapeImages.hero.modernDesign}
        ctaText="Schedule Consultation"
        ctaLink="/contact"
        height="medium"
      />

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-serif font-bold text-dark-green mb-8"
            >
              Medford's Trusted Landscape Design Experts
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-lg text-gray-600 leading-relaxed"
            >
              Medford's diverse neighborhoods and unique topography require specialized landscape design expertise.
              We create beautiful, functional outdoor spaces that work with your property's natural features
              while enhancing your family's lifestyle.
            </motion.p>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Medford Landscape Design Services
          </motion.h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {medfordServices.map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card
                  title={service.name}
                  description={service.description}
                  link={service.link}
                  image={landscapeImages.gallery.backyard}
                />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-4">
                Ready to Transform Your Medford Property?
              </h2>
              <p className="text-lg text-gray-600">
                Contact us today for a free consultation and discover how we can enhance your outdoor space.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <ContactForm />
            </motion.div>
          </div>
        </div>
      </section>
    </>
  );
};

export default LandscapeDesignerMedford;
