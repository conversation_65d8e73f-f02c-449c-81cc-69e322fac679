import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Card from '../../../components/ui/Card';
import ContactForm from '../../../components/forms/ContactForm';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { landscapeImages } from '../../../utils/images';

const LandscapeDesignerCambridge: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Locations', url: 'https://americanelmlandscape.com/locations' },
    { name: 'Landscape Designer Cambridge', url: 'https://americanelmlandscape.com/locations/cambridge/landscape-designer' }
  ];

  const cambridgeServices = [
    {
      name: 'Harvard Square Area Landscaping',
      description: 'Sophisticated landscape design for Cambridge\'s historic Harvard Square neighborhood, blending academic elegance with modern functionality.',
      link: '/services/urban-landscape-design/high-end-landscape-design'
    },
    {
      name: 'MIT Area Modern Design',
      description: 'Contemporary landscape solutions for Cambridge\'s tech corridor, featuring innovative design and sustainable practices.',
      link: '/services/urban-landscape-design/modern-landscape-design'
    },
    {
      name: 'Porter Square Garden Design',
      description: 'Custom garden design for Porter Square properties, maximizing green space in urban environments.',
      link: '/services/urban-landscape-design/small-yard-landscape-design-build'
    },
    {
      name: 'Cambridge Common Area Patios',
      description: 'Professional patio installation near Cambridge Common, designed for entertaining and relaxation.',
      link: '/services/custom-hardscaping-patios/custom-patio-installation'
    },
    {
      name: 'Synthetic Turf for Cambridge Yards',
      description: 'Low-maintenance artificial grass solutions perfect for Cambridge\'s busy professionals and families.',
      link: '/services/synthetic-turf-installation/artificial-grass-small-yards'
    },
    {
      name: 'Outdoor Kitchen Installation',
      description: 'Custom outdoor kitchens designed for Cambridge\'s entertaining culture and year-round outdoor living.',
      link: '/services/outdoor-living-features/outdoor-kitchen-installation'
    }
  ];

  const nearbyAreas = [
    { name: 'Somerville', path: '/locations/somerville/landscape-designer' },
    { name: 'Arlington', path: '/locations/arlington/landscape-designer' },
    { name: 'Back Bay Boston', path: '/locations/boston/landscape-designer-back-bay' },
    { name: 'Brookline', path: '/locations/brookline/landscape-designer' }
  ];

  return (
    <>
      <SEO
        title="Landscape Designer Cambridge | Harvard Square & MIT Area Specialists | American Elm Landscape"
        description="Expert landscape design for Cambridge, MA properties. Specializing in Harvard Square, MIT area, and Porter Square landscaping. Modern design meets academic elegance."
        keywords="landscape designer Cambridge, Cambridge landscaping, Harvard Square landscaping, MIT area landscape design, Porter Square garden design, Cambridge outdoor living"
        canonical="https://americanelmlandscape.com/locations/cambridge/landscape-designer"
        schema={[
          createServiceSchema(
            'Landscape Designer Cambridge',
            'Professional landscape design services for Cambridge, MA properties, including Harvard Square, MIT area, and Porter Square neighborhoods.',
            'Cambridge, MA'
          ),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      {/* Hero Section */}
      <Hero
        title="Landscape Designer Cambridge"
        subtitle="Where Academic Excellence Meets Outdoor Innovation"
        backgroundImage={landscapeImages.hero.urbanDesign}
        ctaText="Schedule Consultation"
        ctaLink="/contact"
        height="medium"
      />

      {/* Breadcrumbs */}
      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-500">Locations</span>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Landscape Designer Cambridge</span>
          </nav>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-serif font-bold text-dark-green mb-8"
            >
              Cambridge's Premier Landscape Design Firm
            </motion.h1>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="prose prose-lg max-w-none"
            >
              <p className="text-lg text-gray-600 leading-relaxed mb-6">
                Cambridge, home to Harvard University and MIT, demands landscape design that reflects
                its unique blend of historic charm and cutting-edge innovation. American Elm Landscape
                specializes in creating sophisticated outdoor spaces that complement Cambridge's
                intellectual atmosphere while providing practical solutions for urban living.
              </p>

              <p className="text-lg text-gray-600 leading-relaxed mb-6">
                From Harvard Square's historic brownstones to the modern developments near MIT,
                we understand the diverse architectural styles and urban challenges that define
                Cambridge neighborhoods. Our designs seamlessly integrate with the city's academic
                culture while maximizing the potential of every outdoor space.
              </p>

              <p className="text-lg text-gray-600 leading-relaxed">
                Whether you're a professor seeking a peaceful garden retreat, a tech professional
                wanting a modern entertaining space, or a family needing a functional outdoor area,
                we create landscapes that enhance your Cambridge lifestyle.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Cambridge Neighborhoods Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Cambridge Neighborhoods We Serve
          </motion.h2>

          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-white p-6 rounded-lg shadow-lg"
            >
              <h3 className="text-xl font-serif font-bold text-dark-green mb-3">Harvard Square</h3>
              <p className="text-gray-600 mb-4">
                Historic charm meets modern sophistication in Harvard Square. We design landscapes
                that complement the area's academic prestige while providing functional outdoor spaces.
              </p>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Historic brownstone landscaping</li>
                <li>• Small courtyard design</li>
                <li>• Academic-inspired gardens</li>
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="bg-white p-6 rounded-lg shadow-lg"
            >
              <h3 className="text-xl font-serif font-bold text-dark-green mb-3">MIT Area</h3>
              <p className="text-gray-600 mb-4">
                Innovation and technology inspire our designs in the MIT corridor. Modern, sustainable
                landscapes that reflect the area's forward-thinking culture.
              </p>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Contemporary design solutions</li>
                <li>• Sustainable landscaping</li>
                <li>• Tech-professional friendly spaces</li>
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-white p-6 rounded-lg shadow-lg"
            >
              <h3 className="text-xl font-serif font-bold text-dark-green mb-3">Porter Square</h3>
              <p className="text-gray-600 mb-4">
                Diverse and vibrant Porter Square properties benefit from our creative approach
                to maximizing green space in urban environments.
              </p>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Urban garden design</li>
                <li>• Multi-family property landscaping</li>
                <li>• Community-focused spaces</li>
              </ul>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Cambridge Landscape Design Services
          </motion.h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {cambridgeServices.map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card
                  title={service.name}
                  description={service.description}
                  link={service.link}
                  image={landscapeImages.gallery.backyard}
                />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-16 bg-dark-green text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-3xl md:text-4xl font-serif font-bold mb-8"
            >
              Why Cambridge Chooses American Elm Landscape
            </motion.h2>

            <div className="grid md:grid-cols-3 gap-8 mt-12">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-dark-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-3">Historic Property Expertise</h3>
                <p className="text-gray-300">
                  Specialized knowledge of Cambridge's historic properties and preservation requirements.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-dark-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-3">Innovation & Sustainability</h3>
                <p className="text-gray-300">
                  Cutting-edge design solutions that reflect Cambridge's culture of innovation.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-dark-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-3">Academic Community Focus</h3>
                <p className="text-gray-300">
                  Understanding the unique needs of Cambridge's academic and professional community.
                </p>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Nearby Areas Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl font-serif font-bold text-dark-green text-center mb-8"
          >
            We Also Serve Nearby Areas
          </motion.h2>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex flex-wrap justify-center gap-4"
          >
            {nearbyAreas.map((area, index) => (
              <Link
                key={index}
                to={area.path}
                className="bg-white px-6 py-3 rounded-lg shadow-md hover:shadow-lg transition-shadow text-dark-green hover:text-light-green font-medium"
              >
                {area.name}
              </Link>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-4">
                Ready to Transform Your Cambridge Property?
              </h2>
              <p className="text-lg text-gray-600">
                Contact us today for a free consultation and discover how we can enhance your outdoor space.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <ContactForm />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Local SEO Section */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h3 className="text-2xl font-serif font-bold text-dark-green mb-4">
                Cambridge Landscape Design Excellence
              </h3>
              <p className="text-gray-600 leading-relaxed">
                American Elm Landscape is proud to serve Cambridge, Massachusetts with professional
                landscape design and installation services. From Harvard Square to Porter Square,
                we bring expertise in urban landscape design, sustainable practices, and historic
                property landscaping to every project. Our Cambridge clients trust us for custom
                hardscaping, synthetic turf installation, outdoor living features, and complete
                landscape transformations that enhance both property value and quality of life.
              </p>
            </motion.div>
          </div>
        </div>
      </section>
    </>
  );
};

export default LandscapeDesignerCambridge;
