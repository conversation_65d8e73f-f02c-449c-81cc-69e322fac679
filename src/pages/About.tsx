import React from 'react';
import { motion } from 'framer-motion';
import SEO from '../components/common/SEO';
import Hero from '../components/common/Hero';
import SocialShowcase from '../components/ui/SocialShowcase';
import { landscapeImages } from '../utils/images';

const About: React.FC = () => {
  return (
    <>
      <SEO
        title="About American Elm Landscape | Boston's Premier Landscape Design Firm"
        description="Learn about American Elm Landscape, Boston's premier landscape design and build firm. Our story, team, and commitment to excellence in urban landscape design."
        keywords="about American Elm Landscape, Boston landscape design firm, landscape design team, company history"
      />

      <Hero
        title="About American Elm Landscape"
        subtitle="Boston's Premier Urban Landscape Design & Build Firm"
        backgroundImage={landscapeImages.hero.default}
        height="medium"
      />

      {/* Company Story Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6">
                Our Story
              </h2>
              <p className="text-lg text-gray-600 leading-relaxed">
                American Elm Landscape is Boston's premier landscape design and build firm,
                specializing in transforming urban outdoor spaces into luxurious retreats.
                With years of experience serving the Greater Boston area, we combine innovative
                design with expert craftsmanship to create stunning outdoor environments.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 gap-12 items-center">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
              >
                <h3 className="text-2xl font-serif font-bold text-dark-green mb-4">
                  Our Mission
                </h3>
                <p className="text-gray-600 mb-6">
                  We believe every outdoor space has the potential to become extraordinary.
                  Our mission is to transform challenging urban environments into beautiful,
                  functional landscapes that enhance your lifestyle and property value.
                </p>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-dark-green rounded-full mr-3"></span>
                    High-end urban landscape design
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-dark-green rounded-full mr-3"></span>
                    Custom hardscaping and patios
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-dark-green rounded-full mr-3"></span>
                    Synthetic turf installation
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-dark-green rounded-full mr-3"></span>
                    Outdoor living features
                  </li>
                </ul>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <img
                  src={landscapeImages.gallery.backyard}
                  alt="American Elm Landscape team at work"
                  className="rounded-lg shadow-lg"
                />
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Social Media Showcase */}
      <SocialShowcase
        title="See Our Work in Action"
        subtitle="Follow our latest projects and transformations on social media"
        className="bg-gray-50"
      />

      {/* Values Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6">
              Why Choose American Elm Landscape
            </h2>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-dark-green rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-dark-green mb-3">
                Expert Craftsmanship
              </h3>
              <p className="text-gray-600">
                Years of experience in high-end landscape design and construction,
                ensuring every project meets the highest standards of quality.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-dark-green rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-dark-green mb-3">
                Urban Specialists
              </h3>
              <p className="text-gray-600">
                Specialized expertise in urban landscape challenges, maximizing
                small spaces and working within city constraints.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-dark-green rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-dark-green mb-3">
                Client Focused
              </h3>
              <p className="text-gray-600">
                Dedicated to understanding your vision and delivering personalized
                solutions that exceed expectations.
              </p>
            </motion.div>
          </div>
        </div>
      </section>
    </>
  );
};

export default About;
