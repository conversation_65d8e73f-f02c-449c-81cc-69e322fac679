import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import SEO from '../components/common/SEO';
import Button from '../components/ui/Button';

const NotFound: React.FC = () => {
  return (
    <>
      <SEO
        title="Page Not Found | American Elm Landscape"
        description="The page you're looking for doesn't exist. Return to our homepage to explore our landscape design services in Greater Boston."
      />

      <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
        <div className="max-w-md w-full text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-6xl font-bold text-dark-green mb-4">404</h1>
            <h2 className="text-2xl font-serif font-semibold text-gray-900 mb-6">
              Page Not Found
            </h2>
            <p className="text-gray-600 mb-8">
              The page you're looking for doesn't exist. Let's get you back to exploring our landscape design services.
            </p>
            
            <div className="space-y-4">
              <Button to="/" size="lg" className="w-full">
                Return to Homepage
              </Button>
              
              <div className="grid grid-cols-1 gap-2 text-sm">
                <Link to="/urban-landscape-design" className="text-dark-green hover:text-light-green transition-colors">
                  Urban Landscape Design
                </Link>
                <Link to="/custom-hardscaping-patios" className="text-dark-green hover:text-light-green transition-colors">
                  Custom Hardscaping & Patios
                </Link>
                <Link to="/synthetic-turf-installation" className="text-dark-green hover:text-light-green transition-colors">
                  Synthetic Turf Installation
                </Link>
                <Link to="/outdoor-living-features" className="text-dark-green hover:text-light-green transition-colors">
                  Outdoor Living Features
                </Link>
                <Link to="/contact" className="text-dark-green hover:text-light-green transition-colors">
                  Contact Us
                </Link>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </>
  );
};

export default NotFound;
