import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import SEO from '../components/common/SEO';
import Hero from '../components/common/Hero';
import Card from '../components/ui/Card';
import Gallery from '../components/ui/Gallery';
import ContactForm from '../components/forms/ContactForm';
import { createLocalBusinessSchema } from '../utils/schema';
import { services, locations } from '../utils/constants';

const Homepage: React.FC = () => {
  const galleryImages = [
    {
      src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_800/u_https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df238b9a13eb2fbb54f7b4.png',
      alt: 'Urban backyard transformation in Boston with custom patio and landscaping',
      caption: 'Complete backyard transformation in Back Bay, Boston'
    },
    {
      src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_800/u_https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df238b897ef525cd9e0da7.png',
      alt: 'Custom patio installation with fire pit in Cambridge',
      caption: 'Luxury patio with custom fire pit installation'
    },
    {
      src: 'https://images.unsplash.com/photo-1585320806297-9794b3e4eeae?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Synthetic turf installation in small urban yard',
      caption: 'Pet-friendly synthetic turf in Somerville'
    },
    {
      src: 'https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Outdoor kitchen installation in Boston',
      caption: 'Custom outdoor kitchen with stone countertops'
    },
    {
      src: 'https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Modern landscape design with clean lines',
      caption: 'Contemporary landscape design in Brookline'
    },
    {
      src: 'https://images.unsplash.com/photo-1600607687644-aac4c3eac7f4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Water feature installation in urban garden',
      caption: 'Custom water feature with natural stone'
    }
  ];

  return (
    <>
      <SEO
        title="Landscape Designer Boston | High-End Urban Design & Build | American Elm Landscape"
        description="Premier landscape designer in Boston specializing in urban design, custom hardscaping, synthetic turf, and outdoor living features. Serving Greater Boston with luxury landscape solutions."
        keywords="landscape designer Boston, urban landscape design, custom hardscaping Boston, synthetic turf installation, outdoor living features, landscape design Cambridge, Somerville landscaping"
        canonical="https://americanelmlandscape.com/"
        schema={createLocalBusinessSchema()}
      />

      {/* Hero Section */}
      <Hero
        title="Landscape Designer Boston"
        subtitle="High-End Urban Design & Build | Transforming Greater Boston's Outdoor Spaces"
        backgroundImage="https://images.leadconnectorhq.com/image/f_webp/q_80/r_1920/u_https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df238b9a13eb2fbb54f7b4.png"
        ctaText="Get Free Consultation"
        ctaLink="/contact"
        height="large"
      />

      {/* Introduction Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6"
            >
              Boston's Premier Urban Landscape Design Firm
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-lg text-gray-700 leading-relaxed mb-8"
            >
              American Elm Landscape specializes in transforming Greater Boston's urban outdoor spaces into luxurious, 
              functional environments. From compact Back Bay courtyards to expansive Cambridge estates, we deliver 
              high-end landscape design and build services tailored to the unique challenges of urban living.
            </motion.p>
            <motion.p
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="text-lg text-gray-700 leading-relaxed"
            >
              Our expert team understands Boston's dense lots, historic building codes, and weather patterns, 
              creating sustainable, beautiful landscapes that enhance your property value and outdoor lifestyle.
            </motion.p>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Our Landscape Design Services
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
            {services.categories.map((service, index) => (
              <motion.div
                key={service.path}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <div className="bg-white rounded-lg shadow-lg p-8 h-full">
                  <h3 className="text-2xl font-serif font-semibold text-dark-green mb-4">
                    <Link to={service.path} className="hover:text-light-green transition-colors">
                      {service.name} Boston
                    </Link>
                  </h3>
                  <p className="text-gray-700 mb-6 leading-relaxed">
                    {service.description}
                  </p>
                  <Link
                    to={service.path}
                    className="inline-flex items-center text-dark-green font-semibold hover:text-light-green transition-colors"
                  >
                    Explore {service.name}
                    <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Featured Services Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card
              title="Outdoor Living Space Design Boston"
              description="Transform your urban property into a comprehensive outdoor living space that maximizes every square foot of your Boston area yard."
              image="/images/outdoor-living-space.jpg"
              imageAlt="Outdoor living space design in Boston with seating and fire pit"
              link="/urban-landscape-design/outdoor-living-space-design"
              ctaText="Design Your Space"
            />
            <Card
              title="Custom Patio Installation Boston"
              description="Professional patio installation using premium materials and expert craftsmanship, designed for Boston's climate and urban constraints."
              image="/images/custom-patio-installation.jpg"
              imageAlt="Custom patio installation in Boston with natural stone"
              link="/custom-hardscaping-patios/custom-patio-installation"
              ctaText="View Patio Options"
            />
            <Card
              title="Synthetic Turf Installation Boston"
              description="Low-maintenance, year-round green spaces perfect for Boston's urban yards, pet areas, and rooftop installations."
              image="/images/synthetic-turf-installation.jpg"
              imageAlt="Synthetic turf installation in Boston urban yard"
              link="/synthetic-turf-installation/synthetic-grass-installation"
              ctaText="Explore Turf Options"
            />
          </div>
        </div>
      </section>

      {/* Service Areas Section */}
      <section className="py-20 bg-dark-green text-white relative overflow-hidden">
        {/* Decorative background elements */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-0 left-0 w-96 h-96 bg-white rounded-full blur-3xl -translate-x-1/2 -translate-y-1/2"></div>
          <div className="absolute bottom-0 right-0 w-96 h-96 bg-white rounded-full blur-3xl translate-x-1/2 translate-y-1/2"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-serif font-bold mb-4">
              Serving Greater Boston's Finest Neighborhoods
            </h2>
            <div className="w-24 h-1 bg-white/30 mx-auto"></div>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16 max-w-6xl mx-auto">
            {locations.map((location, index) => (
              <motion.div
                key={location.name}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 hover:bg-white/10 hover:border-white/20 transition-all duration-300"
              >
                <Link
                  to={location.path}
                  className="block group"
                >
                  <h3 className="text-xl font-serif font-semibold text-white group-hover:text-gray-200 transition-colors">
                    {location.name}
                  </h3>
                  <p className="text-sm text-gray-400 mt-2 group-hover:text-gray-300 transition-colors">
                    {location.subtitle}
                  </p>
                </Link>
              </motion.div>
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-center max-w-3xl mx-auto"
          >
            <p className="text-lg text-gray-300 mb-8 leading-relaxed">
              From the historic brownstones of Back Bay to the modern condos of Cambridge,
              we understand each neighborhood's unique character and requirements.
            </p>
            <Link
              to="/contact"
              className="inline-block bg-white text-dark-green px-10 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105"
            >
              Find Your Local Designer
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Recent Boston Area Projects
          </motion.h2>
          
          <Gallery images={galleryImages} columns={3} />
        </div>
      </section>

      {/* Video Showcase Section */}
      <section className="py-16 bg-dark-green">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-white text-center mb-12"
          >
            Project Transformations
          </motion.h2>

          <div className="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-white rounded-lg overflow-hidden shadow-lg"
            >
              <video
                controls
                className="w-full h-64 object-cover"
                poster="https://images.leadconnectorhq.com/image/f_webp/q_80/r_800/u_https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df238b9a13eb2fbb54f7b4.png"
              >
                <source src="https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df23dc85bf794407368e2e.mp4" type="video/mp4" />
                Your browser does not support the video tag.
              </video>
              <div className="p-6">
                <h3 className="text-xl font-serif font-bold text-dark-green mb-2">
                  Complete Backyard Transformation
                </h3>
                <p className="text-gray-600">
                  Watch this stunning transformation of a Boston area backyard featuring custom hardscaping, outdoor living spaces, and professional landscape design.
                </p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-white rounded-lg overflow-hidden shadow-lg"
            >
              <video
                controls
                className="w-full h-64 object-cover"
                poster="https://images.leadconnectorhq.com/image/f_webp/q_80/r_800/u_https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df238b897ef525cd9e0da7.png"
              >
                <source src="https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df23dc065f28bdd4e9d55c.mp4" type="video/mp4" />
                Your browser does not support the video tag.
              </video>
              <div className="p-6">
                <h3 className="text-xl font-serif font-bold text-dark-green mb-2">
                  Urban Landscape Design
                </h3>
                <p className="text-gray-600">
                  See how we transform challenging urban spaces into beautiful, functional outdoor environments with expert design and premium materials.
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6">
                Ready to Transform Your Outdoor Space?
              </h2>
              <p className="text-lg text-gray-700">
                Schedule your free consultation with Boston's premier landscape design team. 
                We'll discuss your vision, assess your space, and create a custom design plan.
              </p>
            </motion.div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              <ContactForm />
              
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="space-y-8"
              >
                <div>
                  <h3 className="text-xl font-serif font-semibold text-dark-green mb-4">
                    Why Choose American Elm Landscape?
                  </h3>
                  <ul className="space-y-3 text-gray-700">
                    <li className="flex items-start">
                      <svg className="w-5 h-5 text-dark-green mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      15+ years of experience in Greater Boston landscape design
                    </li>
                    <li className="flex items-start">
                      <svg className="w-5 h-5 text-dark-green mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Specialized expertise in urban and small-space design
                    </li>
                    <li className="flex items-start">
                      <svg className="w-5 h-5 text-dark-green mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Full design-build services from concept to completion
                    </li>
                    <li className="flex items-start">
                      <svg className="w-5 h-5 text-dark-green mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Licensed, insured, and committed to quality
                    </li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-xl font-serif font-semibold text-dark-green mb-4">
                    Our Process
                  </h3>
                  <div className="space-y-4">
                    <div className="flex items-start">
                      <div className="w-8 h-8 bg-dark-green text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 flex-shrink-0">
                        1
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">Consultation & Site Analysis</h4>
                        <p className="text-gray-600 text-sm">We visit your property to understand your vision and assess the space.</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-8 h-8 bg-dark-green text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 flex-shrink-0">
                        2
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">Custom Design Development</h4>
                        <p className="text-gray-600 text-sm">Our team creates detailed plans tailored to your needs and budget.</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-8 h-8 bg-dark-green text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 flex-shrink-0">
                        3
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">Professional Installation</h4>
                        <p className="text-gray-600 text-sm">Expert installation with attention to detail and quality craftsmanship.</p>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Homepage;
