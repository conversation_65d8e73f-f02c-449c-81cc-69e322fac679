import { motion } from 'framer-motion';
import SEO from '../components/common/SEO';
import Hero from '../components/common/Hero';
import { createBreadcrumbSchema } from '../utils/schema';
import { landscapeImages } from '../utils/images';

const Blog: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Blog', url: 'https://americanelmlandscape.com/blog' }
  ];

  const blogCategories = [
    { name: 'Design Tips', icon: '🎨', description: 'Expert advice on landscape design for urban spaces' },
    { name: 'Plant Selection', icon: '🌿', description: 'Best plants for Boston climate and conditions' },
    { name: 'Hardscaping', icon: '🏛️', description: 'Patio, walkway, and hardscape inspiration' },
    { name: 'Maintenance', icon: '🛠️', description: 'Seasonal care and maintenance guides' }
  ];

  const comingSoonFeatures = [
    'Seasonal landscaping guides',
    'Before & after project showcases',
    'Plant care and selection tips',
    'Urban gardening techniques',
    'Hardscaping design ideas',
    'Sustainable landscaping practices',
    'Boston-specific landscape advice',
    'DIY vs. professional guidance'
  ];

  return (
    <>
      <SEO
        title="Landscape Design Blog | Tips & Trends | American Elm Landscape"
        description="Landscape design tips, trends, and insights from Boston's premier landscape design firm. Expert advice for urban outdoor spaces."
        keywords="landscape design blog, Boston landscaping tips, urban garden design, outdoor living trends, landscape maintenance"
        canonical="https://americanelmlandscape.com/blog"
        schema={[createBreadcrumbSchema(breadcrumbs)]}
      />

      <Hero
        title="Landscape Design Blog"
        subtitle="Tips, Trends & Insights for Urban Outdoor Spaces"
        backgroundImage={landscapeImages.hero.blog}
        height="small"
      />

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-4xl md:text-5xl font-serif font-bold text-dark-green mb-6">
                Coming Soon
              </h1>
              <p className="text-xl text-gray-700 mb-8">
                We're currently developing our blog to bring you expert landscape design advice, seasonal tips,
                and inspiration for your Boston outdoor spaces. Check back soon for valuable content!
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Blog Categories
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
            {blogCategories.map((category, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center bg-white p-6 rounded-lg shadow-lg"
              >
                <div className="text-4xl mb-4">{category.icon}</div>
                <h3 className="text-xl font-serif font-semibold text-dark-green mb-3">{category.name}</h3>
                <p className="text-gray-700">{category.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
            >
              What to Expect
            </motion.h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {comingSoonFeatures.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="flex items-start"
                >
                  <svg className="w-6 h-6 text-dark-green mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-lg text-gray-700">{feature}</span>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-dark-green text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-3xl font-serif font-bold mb-6">Need Landscape Advice Now?</h2>
              <p className="text-xl text-gray-200 mb-8">
                While our blog is under development, our team is ready to answer your landscape design questions.
                Contact us for a free consultation and expert advice for your Boston property.
              </p>
              <a
                href="/contact"
                className="inline-block bg-white text-dark-green px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
              >
                Contact Us Today
              </a>
            </motion.div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Blog;
