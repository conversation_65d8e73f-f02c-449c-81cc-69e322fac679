import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { Trees, Sparkles, Home as HomeIcon, Maximize2, Palette } from 'lucide-react';
import SEO from '../../components/common/SEO';
import Hero from '../../components/common/Hero';
import ServiceCard from '../../components/ui/ServiceCard';
import Gallery from '../../components/ui/Gallery';
import ContactForm from '../../components/forms/ContactForm';
import { createServiceSchema, createBreadcrumbSchema } from '../../utils/schema';
import { services, locations } from '../../utils/constants';

const UrbanLandscapeDesign: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Urban Landscape Design Boston', url: 'https://americanelmlandscape.com/urban-landscape-design' }
  ];

  const serviceIcons: { [key: string]: React.ReactNode } = {
    'Outdoor Living Space Design': <HomeIcon className="w-12 h-12" />,
    'Urban Backyard Transformation': <Trees className="w-12 h-12" />,
    'Small Yard Landscape Design & Build': <Maximize2 className="w-12 h-12" />,
    'High-End Landscape Design': <Sparkles className="w-12 h-12" />,
    'Modern Landscape Design': <Palette className="w-12 h-12" />
  };

  const galleryImages = [
    {
      src: '/images/urban-design-before-after.jpg',
      alt: 'Before and after urban landscape design transformation in Boston',
      caption: 'Complete urban backyard transformation in South End'
    },
    {
      src: '/images/small-yard-design.jpg',
      alt: 'Small yard landscape design with maximized space',
      caption: 'Maximized small yard design in Cambridge'
    },
    {
      src: '/images/modern-urban-landscape.jpg',
      alt: 'Modern urban landscape design with clean lines',
      caption: 'Contemporary design for Back Bay townhouse'
    },
    {
      src: '/images/urban-outdoor-living.jpg',
      alt: 'Urban outdoor living space with seating and plants',
      caption: 'Multi-functional outdoor living space'
    }
  ];

  return (
    <>
      <SEO
        title="Urban Landscape Design Boston | Expert Design & Build | American Elm Landscape"
        description="Transform your urban Boston property with expert landscape design. Specializing in small yards, rooftops, and challenging urban spaces. Free consultation available."
        keywords="urban landscape design Boston, small yard landscaping, Boston landscape designer, urban garden design, city landscaping, rooftop garden design"
        canonical="https://americanelmlandscape.com/urban-landscape-design"
        schema={[
          createServiceSchema(
            'Urban Landscape Design Boston',
            'Professional urban landscape design services for Boston area properties, specializing in small spaces and challenging urban environments.'
          ),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      {/* Hero Section */}
      <Hero
        title="Urban Landscape Design Boston"
        subtitle="Transform Your Urban Space into a Luxurious Outdoor Retreat"
        backgroundImage="https://images.leadconnectorhq.com/image/f_webp/q_80/r_1920/u_https://storage.googleapis.com/msgsndr/4wiC9uJMnxo3MVfPkVqG/media/68df238b9a13eb2fbb54f7b4.png"
        ctaText="Start Your Design"
        ctaLink="/contact"
        height="medium"
      />

      {/* Breadcrumbs */}
      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Urban Landscape Design Boston</span>
          </nav>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6"
            >
              Boston's Urban Landscape Design Specialists
            </motion.h2>
            
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="prose prose-lg max-w-none text-gray-700"
            >
              <p className="text-xl leading-relaxed mb-6">
                Urban landscape design in Greater Boston requires specialized expertise to navigate dense lots, 
                historic building codes, and unique environmental challenges. At American Elm Landscape, we excel 
                at transforming compact urban spaces into stunning outdoor environments that maximize every square foot.
              </p>
              
              <p className="leading-relaxed mb-6">
                From the narrow lots of Back Bay to the historic properties of Cambridge, our urban landscape design 
                services are tailored to Boston's unique urban fabric. We understand how to work within the constraints 
                of city living while creating beautiful, functional outdoor spaces that enhance your property value and lifestyle.
              </p>
              
              <p className="leading-relaxed">
                Our comprehensive approach includes site analysis, custom design development, permitting assistance, 
                and expert installation. Whether you're working with a small courtyard, rooftop space, or challenging 
                sloped lot, we have the experience to create your perfect urban oasis.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Our Urban Landscape Design Services
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.urbanDesign.map((service, index) => (
              <motion.div
                key={service.path}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <ServiceCard
                  title={service.name + ' Boston'}
                  description={service.description + ' Tailored for Greater Boston\'s urban environment and local regulations.'}
                  link={service.path}
                  icon={serviceIcons[service.name]}
                />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-3xl font-serif font-bold text-dark-green mb-6">
                Why Choose Our Urban Design Expertise?
              </h2>
              
              <div className="space-y-6">
                <div className="flex items-start">
                  <div className="w-12 h-12 bg-dark-green text-white rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Local Expertise</h3>
                    <p className="text-gray-700">Deep understanding of Boston's zoning laws, soil conditions, and climate challenges.</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="w-12 h-12 bg-dark-green text-white rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Space Maximization</h3>
                    <p className="text-gray-700">Creative solutions to make small urban spaces feel larger and more functional.</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="w-12 h-12 bg-dark-green text-white rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Sustainable Design</h3>
                    <p className="text-gray-700">Eco-friendly solutions that thrive in Boston's climate while reducing maintenance.</p>
                  </div>
                </div>
              </div>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Gallery images={galleryImages} columns={2} />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Related Locations */}
      <section className="py-16 bg-dark-green text-white">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl font-serif font-bold text-center mb-12"
          >
            Urban Design Services by Location
          </motion.h2>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {locations.slice(0, 8).map((location, index) => (
              <motion.div
                key={location.name}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                {location.neighborhoods ? (
                  <div>
                    <h3 className="text-lg font-semibold mb-2">{location.name}</h3>
                    {location.neighborhoods.map((neighborhood) => (
                      <Link
                        key={neighborhood.path}
                        to={neighborhood.path}
                        className="block text-gray-300 hover:text-white transition-colors text-sm mb-1"
                      >
                        {neighborhood.name} Urban Design
                      </Link>
                    ))}
                  </div>
                ) : (
                  <Link
                    to={location.path!}
                    className="block text-lg font-semibold hover:text-gray-300 transition-colors"
                  >
                    {location.name} Urban Design
                  </Link>
                )}
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6">
                Ready to Transform Your Urban Space?
              </h2>
              <p className="text-lg text-gray-700">
                Let's discuss your urban landscape design project. Our team will assess your space 
                and create a custom design that maximizes your property's potential.
              </p>
            </motion.div>
            
            <ContactForm prefilledService="Urban Landscape Design" />
          </div>
        </div>
      </section>
    </>
  );
};

export default UrbanLandscapeDesign;
