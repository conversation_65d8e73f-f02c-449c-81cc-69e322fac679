import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { Square, Gem, Flame, Footprints, Layers } from 'lucide-react';
import SEO from '../../components/common/SEO';
import Hero from '../../components/common/Hero';
import ServiceCard from '../../components/ui/ServiceCard';
import Gallery from '../../components/ui/Gallery';
import ContactForm from '../../components/forms/ContactForm';
import { createServiceSchema, createBreadcrumbSchema } from '../../utils/schema';
import { landscapeImages } from '../../utils/images';

const CustomHardscapingPatios: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Custom Hardscaping & Patios Boston', url: 'https://americanelmlandscape.com/custom-hardscaping-patios' }
  ];

  const hardscapingServices = [
    {
      name: 'Custom Patio Installation',
      description: 'Professional patio installation using premium materials including natural stone, pavers, and concrete. Designed for Boston\'s climate and urban spaces.',
      link: '/custom-hardscaping-patios/custom-patio-installation',
      icon: <Square className="w-12 h-12" />
    },
    {
      name: 'Luxury Hardscape Design',
      description: 'High-end hardscape design featuring custom stonework, integrated lighting, and sophisticated material combinations for discerning Boston homeowners.',
      link: '/custom-hardscaping-patios/luxury-hardscape-design',
      icon: <Gem className="w-12 h-12" />
    },
    {
      name: 'Fire Pit & Pizza Oven Installation',
      description: 'Custom fire features and outdoor cooking installations that extend your outdoor season and create gathering spaces for family and friends.',
      link: '/custom-hardscaping-patios/fire-pit-pizza-oven-installation',
      icon: <Flame className="w-12 h-12" />
    },
    {
      name: 'Custom Fire Pits',
      description: 'Bespoke fire pit designs using natural stone, steel, and concrete. Perfect for Boston\'s cooler evenings and year-round outdoor entertainment.',
      link: '/custom-hardscaping-patios/custom-fire-pits',
      icon: <Flame className="w-12 h-12" />
    },
    {
      name: 'Walkway & Pathway Installation',
      description: 'Elegant walkways and pathways that connect your outdoor spaces while adding beauty and functionality to your Boston property.',
      link: '/custom-hardscaping-patios/walkway-pathway-installation',
      icon: <Footprints className="w-12 h-12" />
    },
    {
      name: 'Retaining Wall Construction',
      description: 'Structural and decorative retaining walls that solve grading challenges while adding visual interest and usable space to sloped properties.',
      link: '/custom-hardscaping-patios/retaining-wall-construction',
      icon: <Layers className="w-12 h-12" />
    }
  ];

  const galleryImages = [
    {
      src: landscapeImages.hero.hardscaping,
      alt: 'Custom hardscape patio installation in Boston',
      caption: 'Natural stone patio with integrated fire pit'
    },
    {
      src: landscapeImages.gallery.patio,
      alt: 'Luxury patio design with outdoor kitchen',
      caption: 'Complete outdoor living space transformation'
    },
    {
      src: landscapeImages.gallery.hardscape,
      alt: 'Custom walkway and retaining wall installation',
      caption: 'Multi-level hardscape design'
    },
    {
      src: landscapeImages.gallery.firepit,
      alt: 'Custom fire pit installation Boston',
      caption: 'Natural stone fire pit with seating wall'
    }
  ];

  return (
    <>
      <SEO
        title="Custom Hardscaping & Patios Boston | Professional Installation | American Elm Landscape"
        description="Expert custom hardscaping and patio installation in Boston. Luxury outdoor spaces with premium materials and professional craftsmanship for discerning homeowners."
        keywords="custom hardscaping Boston, patio installation Boston, outdoor living spaces, custom patios, fire pit installation, retaining walls, walkways"
        canonical="https://americanelmlandscape.com/custom-hardscaping-patios"
        schema={[
          createServiceSchema(
            'Custom Hardscaping & Patios Boston',
            'Professional custom hardscaping and patio installation services for Boston area properties, specializing in luxury outdoor living spaces and premium materials.'
          ),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      {/* Hero Section */}
      <Hero
        title="Custom Hardscaping & Patios Boston"
        subtitle="Create Stunning Outdoor Living Spaces with Expert Hardscape Design"
        backgroundImage={landscapeImages.hero.hardscaping}
        ctaText="Start Your Project"
        ctaLink="/contact"
        height="medium"
      />

      {/* Breadcrumbs */}
      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Custom Hardscaping & Patios Boston</span>
          </nav>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6"
            >
              Boston's Premier Custom Hardscaping Specialists
            </motion.h2>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="prose prose-lg max-w-none text-gray-700"
            >
              <p className="text-xl leading-relaxed mb-6">
                Transform your Boston outdoor space with custom hardscaping that combines luxury, functionality,
                and durability. Our expert team specializes in creating stunning patios, walkways, retaining walls,
                and fire features that enhance your property value and outdoor living experience.
              </p>

              <p className="text-lg leading-relaxed mb-6">
                From intimate Back Bay courtyards to expansive Brookline estates, we understand the unique
                challenges of Boston's urban environment. Our hardscaping solutions are designed to withstand
                New England's harsh winters while providing year-round beauty and functionality.
              </p>

              <p className="text-lg leading-relaxed">
                Using only premium materials including natural stone, high-quality pavers, and architectural
                concrete, we create outdoor spaces that reflect your personal style while complementing your
                home's architecture and the character of your neighborhood.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Our Custom Hardscaping Services
          </motion.h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {hardscapingServices.map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <ServiceCard
                  title={service.name}
                  description={service.description}
                  link={service.link}
                  icon={service.icon}
                />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Why Choose American Elm for Your Hardscaping Project
          </motion.h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-dark-green rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-dark-green mb-3">Premium Materials</h3>
              <p className="text-gray-600">
                We source only the finest natural stone, pavers, and materials that withstand
                Boston's climate while maintaining their beauty for decades.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-dark-green rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-dark-green mb-3">Expert Craftsmanship</h3>
              <p className="text-gray-600">
                Our skilled artisans bring decades of experience in creating custom hardscapes
                that combine technical precision with artistic vision.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-dark-green rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-dark-green mb-3">Timely Completion</h3>
              <p className="text-gray-600">
                We respect your time and investment with efficient project management
                and clear communication throughout the construction process.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Our Custom Hardscaping Portfolio
          </motion.h2>

          <Gallery images={galleryImages} />
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-dark-green text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-3xl md:text-4xl font-serif font-bold text-center mb-8"
            >
              Ready to Transform Your Outdoor Space?
            </motion.h2>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-xl text-center mb-12"
            >
              Let's discuss your custom hardscaping vision and create an outdoor space that exceeds your expectations.
            </motion.p>

            <ContactForm />
          </div>
        </div>
      </section>

      {/* Local SEO Section */}
      <section className="py-8 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <p className="text-sm text-gray-600">
              <strong>American Elm Landscape</strong> - Professional custom hardscaping and patio installation
              serving Boston, Cambridge, Somerville, Brookline, Back Bay, South End, and surrounding areas.
              Licensed and insured contractors specializing in luxury outdoor living spaces, custom fire features,
              retaining walls, and premium stonework installations.
            </p>
          </div>
        </div>
      </section>
    </>
  );
};

export default CustomHardscapingPatios;
