import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { ChefHat, Lightbulb, Droplets, Home, Umbrella } from 'lucide-react';
import SEO from '../../components/common/SEO';
import Hero from '../../components/common/Hero';
import ServiceCard from '../../components/ui/ServiceCard';
import Gallery from '../../components/ui/Gallery';
import ContactForm from '../../components/forms/ContactForm';
import { createServiceSchema, createBreadcrumbSchema } from '../../utils/schema';
import { landscapeImages } from '../../utils/images';

const OutdoorLivingFeatures: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Outdoor Living Features Boston', url: 'https://americanelmlandscape.com/outdoor-living-features' }
  ];

  const outdoorFeatures = [
    {
      name: 'Outdoor Kitchen Installation',
      description: 'Custom outdoor kitchens with premium appliances, weather-resistant cabinetry, and elegant countertops. Perfect for Boston\'s entertaining culture and year-round outdoor dining.',
      link: '/outdoor-living-features/outdoor-kitchen-installation',
      icon: <ChefHat className="w-12 h-12" />
    },
    {
      name: 'Outdoor Lighting Installation',
      description: 'Professional landscape lighting design and installation to extend your outdoor enjoyment into the evening hours while enhancing security and curb appeal.',
      link: '/outdoor-living-features/outdoor-lighting-installation',
      icon: <Lightbulb className="w-12 h-12" />
    },
    {
      name: 'Water Feature Installation',
      description: 'Custom water features including fountains, waterfalls, and ponds that add tranquility and elegance to your Boston outdoor space.',
      link: '/outdoor-living-features/water-feature-installation',
      icon: <Droplets className="w-12 h-12" />
    },
    {
      name: 'Pergola & Trellis Installation',
      description: 'Beautiful pergolas and trellises that provide shade, define spaces, and support climbing plants for enhanced privacy and visual interest.',
      link: '/outdoor-living-features/pergola-trellis-installation',
      icon: <Home className="w-12 h-12" />
    },
    {
      name: 'Deck & Patio Cover Installation',
      description: 'Custom covers and structures that protect your outdoor furniture and extend the usability of your deck or patio throughout the seasons.',
      link: '/outdoor-living-features/deck-patio-cover-installation',
      icon: <Umbrella className="w-12 h-12" />
    }
  ];

  const galleryImages = [
    {
      src: landscapeImages.hero.outdoorLiving,
      alt: 'Outdoor kitchen installation Boston',
      caption: 'Complete outdoor kitchen with dining area'
    },
    {
      src: landscapeImages.gallery.lighting,
      alt: 'Landscape lighting installation Boston',
      caption: 'Professional landscape lighting design'
    },
    {
      src: landscapeImages.gallery.waterFeature,
      alt: 'Water feature installation Boston',
      caption: 'Custom fountain with landscape integration'
    },
    {
      src: landscapeImages.gallery.pergola,
      alt: 'Pergola installation Boston',
      caption: 'Custom pergola with climbing plants'
    }
  ];

  return (
    <>
      <SEO
        title="Outdoor Living Features Boston | Kitchens, Lighting & Water Features | American Elm Landscape"
        description="Custom outdoor living features in Boston including outdoor kitchens, lighting, water features, pergolas, and covers. Enhance your outdoor experience with professional installation."
        keywords="outdoor living features Boston, outdoor kitchen installation, landscape lighting, water features, pergolas, outdoor covers, patio covers"
        canonical="https://americanelmlandscape.com/outdoor-living-features"
        schema={[
          createServiceSchema(
            'Outdoor Living Features Boston',
            'Professional outdoor living feature installation services for Boston area properties, specializing in outdoor kitchens, lighting, water features, and custom structures.'
          ),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      {/* Hero Section */}
      <Hero
        title="Outdoor Living Features Boston"
        subtitle="Enhance Your Outdoor Experience with Custom Features"
        backgroundImage={landscapeImages.hero.outdoorLiving}
        ctaText="Explore Features"
        ctaLink="/contact"
        height="medium"
      />

      {/* Breadcrumbs */}
      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Outdoor Living Features Boston</span>
          </nav>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6"
            >
              Transform Your Outdoor Space with Custom Living Features
            </motion.h2>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="prose prose-lg max-w-none text-gray-700"
            >
              <p className="text-xl leading-relaxed mb-6">
                Elevate your Boston outdoor living experience with custom features that extend your home's
                functionality into the landscape. From sophisticated outdoor kitchens to elegant water features,
                we create outdoor spaces that become the heart of your home's entertainment and relaxation areas.
              </p>

              <p className="text-lg leading-relaxed mb-6">
                Our outdoor living features are designed specifically for Boston's climate and urban environment.
                Whether you're hosting dinner parties in Back Bay, creating a peaceful retreat in Cambridge,
                or adding functionality to a compact Somerville yard, we craft custom solutions that enhance
                your lifestyle and property value.
              </p>

              <p className="text-lg leading-relaxed">
                Each installation combines premium materials, expert craftsmanship, and thoughtful design to
                create outdoor features that are both beautiful and functional, providing years of enjoyment
                for you and your family.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Our Outdoor Living Features
          </motion.h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {outdoorFeatures.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <ServiceCard
                  title={feature.name}
                  description={feature.description}
                  link={feature.link}
                  icon={feature.icon}
                />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Why Invest in Outdoor Living Features
          </motion.h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-dark-green rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-1a1 1 0 011-1h2a1 1 0 011 1v1a1 1 0 001 1m-6 0h6" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-dark-green mb-3">Increased Home Value</h3>
              <p className="text-gray-600">
                Professional outdoor living features significantly increase your property value
                and appeal to potential buyers in Boston's competitive market.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-dark-green rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-dark-green mb-3">Enhanced Entertainment</h3>
              <p className="text-gray-600">
                Create the perfect setting for hosting friends and family with outdoor kitchens,
                lighting, and comfortable gathering spaces.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-dark-green rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-dark-green mb-3">Extended Seasons</h3>
              <p className="text-gray-600">
                Enjoy your outdoor space longer with features like fire pits, lighting,
                and covered areas that extend usability through Boston's seasons.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-dark-green rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-dark-green mb-3">Personal Wellness</h3>
              <p className="text-gray-600">
                Create peaceful retreats with water features and comfortable seating areas
                that promote relaxation and mental well-being.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-dark-green rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-dark-green mb-3">Energy Efficiency</h3>
              <p className="text-gray-600">
                LED landscape lighting and efficient outdoor appliances reduce energy costs
                while providing beautiful illumination and functionality.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-dark-green rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-dark-green mb-3">Enhanced Security</h3>
              <p className="text-gray-600">
                Professional lighting installation improves security and safety while
                highlighting your landscape's best features during evening hours.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Our Outdoor Living Feature Installations
          </motion.h2>

          <Gallery images={galleryImages} />
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-dark-green text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-3xl md:text-4xl font-serif font-bold text-center mb-8"
            >
              Ready to Enhance Your Outdoor Living Experience?
            </motion.h2>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-xl text-center mb-12"
            >
              Let's create custom outdoor features that transform your Boston property into an outdoor paradise.
            </motion.p>

            <ContactForm />
          </div>
        </div>
      </section>

      {/* Local SEO Section */}
      <section className="py-8 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <p className="text-sm text-gray-600">
              <strong>American Elm Landscape</strong> - Professional outdoor living feature installation
              serving Boston, Cambridge, Somerville, Brookline, Back Bay, South End, and surrounding areas.
              Licensed and insured contractors specializing in outdoor kitchens, landscape lighting, water features,
              pergolas, and custom outdoor structures.
            </p>
          </div>
        </div>
      </section>
    </>
  );
};

export default OutdoorLivingFeatures;
