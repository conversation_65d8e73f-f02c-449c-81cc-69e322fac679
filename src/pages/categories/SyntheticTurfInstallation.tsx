import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { Sprout, PawPrint, Target, Leaf } from 'lucide-react';
import SEO from '../../components/common/SEO';
import Hero from '../../components/common/Hero';
import ServiceCard from '../../components/ui/ServiceCard';
import Gallery from '../../components/ui/Gallery';
import ContactForm from '../../components/forms/ContactForm';
import { createServiceSchema, createBreadcrumbSchema } from '../../utils/schema';
import { landscapeImages } from '../../utils/images';

const SyntheticTurfInstallation: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Synthetic Turf Installation Boston', url: 'https://americanelmlandscape.com/synthetic-turf-installation' }
  ];

  const turfServices = [
    {
      name: 'Artificial Grass for Small Yards',
      description: 'Transform compact Boston yards with premium artificial grass that stays green year-round. Perfect for urban properties with limited space and challenging growing conditions.',
      link: '/synthetic-turf-installation/artificial-grass-small-yards',
      icon: <Sprout className="w-12 h-12" />
    },
    {
      name: 'Synthetic Grass Installation',
      description: 'Professional installation of high-quality synthetic grass systems with proper drainage and base preparation for long-lasting, beautiful results.',
      link: '/synthetic-turf-installation/synthetic-grass-installation',
      icon: <Leaf className="w-12 h-12" />
    },
    {
      name: 'Pet-Friendly Artificial Turf',
      description: 'Specialized pet-friendly turf systems with antimicrobial properties and superior drainage, perfect for Boston dog owners seeking low-maintenance solutions.',
      link: '/synthetic-turf-installation/pet-friendly-artificial-turf',
      icon: <PawPrint className="w-12 h-12" />
    },
    {
      name: 'Putting Green Installation',
      description: 'Custom putting greens and golf practice areas using professional-grade synthetic turf. Bring the golf course experience to your Boston backyard.',
      link: '/synthetic-turf-installation/putting-green-installation',
      icon: <Target className="w-12 h-12" />
    }
  ];

  const galleryImages = [
    {
      src: landscapeImages.hero.syntheticTurf,
      alt: 'Synthetic turf installation in Boston backyard',
      caption: 'Complete backyard transformation with premium artificial grass'
    },
    {
      src: landscapeImages.gallery.turf,
      alt: 'Pet-friendly artificial turf installation',
      caption: 'Pet-friendly turf with integrated drainage system'
    },
    {
      src: landscapeImages.gallery.smallYard,
      alt: 'Small yard artificial grass installation Boston',
      caption: 'Maximized small urban space with synthetic turf'
    },
    {
      src: landscapeImages.gallery.puttingGreen,
      alt: 'Custom putting green installation Boston',
      caption: 'Professional putting green with landscape integration'
    }
  ];

  return (
    <>
      <SEO
        title="Synthetic Turf Installation Boston | Artificial Grass Experts | American Elm Landscape"
        description="Professional synthetic turf installation in Boston. Low-maintenance artificial grass solutions for urban yards, pet areas, rooftops, and putting greens."
        keywords="synthetic turf Boston, artificial grass installation, pet-friendly turf, low maintenance landscaping, putting green installation, artificial grass Boston"
        canonical="https://americanelmlandscape.com/synthetic-turf-installation"
        schema={[
          createServiceSchema(
            'Synthetic Turf Installation Boston',
            'Professional synthetic turf and artificial grass installation services for Boston area properties, specializing in low-maintenance, year-round green solutions.'
          ),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      {/* Hero Section */}
      <Hero
        title="Synthetic Turf Installation Boston"
        subtitle="Low-Maintenance, Year-Round Green Spaces for Urban Properties"
        backgroundImage={landscapeImages.hero.syntheticTurf}
        ctaText="Get Free Estimate"
        ctaLink="/contact"
        height="medium"
      />

      {/* Breadcrumbs */}
      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Synthetic Turf Installation Boston</span>
          </nav>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-6"
            >
              Boston's Premier Synthetic Turf Installation Experts
            </motion.h2>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="prose prose-lg max-w-none text-gray-700"
            >
              <p className="text-xl leading-relaxed mb-6">
                Experience the beauty of a perfect lawn year-round with professional synthetic turf installation.
                Our premium artificial grass solutions are ideal for Boston's challenging climate, providing
                lush green spaces that require no watering, mowing, or seasonal maintenance.
              </p>

              <p className="text-lg leading-relaxed mb-6">
                From compact Back Bay courtyards to expansive Brookline estates, synthetic turf offers the
                perfect solution for urban properties where natural grass struggles. Our installations feature
                advanced drainage systems, realistic textures, and UV-resistant materials designed to withstand
                New England's harsh winters and hot summers.
              </p>

              <p className="text-lg leading-relaxed">
                Whether you're looking to create a pet-friendly play area, a low-maintenance landscape solution,
                or a custom putting green, our expert team delivers installations that look natural and perform
                beautifully for years to come.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Our Synthetic Turf Services
          </motion.h2>

          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {turfServices.map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <ServiceCard
                  title={service.name}
                  description={service.description}
                  link={service.link}
                  icon={service.icon}
                />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Why Choose Synthetic Turf for Your Boston Property
          </motion.h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-dark-green rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-dark-green mb-3">Year-Round Beauty</h3>
              <p className="text-gray-600">
                Maintain a lush, green lawn through Boston's harsh winters and hot summers
                without seasonal brown patches or dormancy periods.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-dark-green rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-dark-green mb-3">Zero Maintenance</h3>
              <p className="text-gray-600">
                Eliminate mowing, watering, fertilizing, and pest control. Save time and money
                while enjoying a perfect lawn every day.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-dark-green rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-dark-green mb-3">Pet & Family Friendly</h3>
              <p className="text-gray-600">
                Safe, non-toxic materials with antimicrobial properties. Perfect for children
                and pets with superior drainage and easy cleanup.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-dark-green rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-dark-green mb-3">Increased Property Value</h3>
              <p className="text-gray-600">
                Professional synthetic turf installation adds lasting value to your property
                with a premium landscape feature that appeals to buyers.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-dark-green rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a4 4 0 004-4V5z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-dark-green mb-3">Water Conservation</h3>
              <p className="text-gray-600">
                Eliminate the need for irrigation and contribute to water conservation efforts
                while maintaining a beautiful green space year-round.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-dark-green rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h3 className="text-xl font-serif font-bold text-dark-green mb-3">Durability & Longevity</h3>
              <p className="text-gray-600">
                Premium materials with UV protection and advanced backing systems ensure
                your investment lasts 15-20 years with minimal wear.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Our Synthetic Turf Installations
          </motion.h2>

          <Gallery images={galleryImages} />
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-dark-green text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-3xl md:text-4xl font-serif font-bold text-center mb-8"
            >
              Ready for a Perfect Lawn Year-Round?
            </motion.h2>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-xl text-center mb-12"
            >
              Discover how synthetic turf can transform your Boston property with a free consultation and estimate.
            </motion.p>

            <ContactForm />
          </div>
        </div>
      </section>

      {/* Local SEO Section */}
      <section className="py-8 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <p className="text-sm text-gray-600">
              <strong>American Elm Landscape</strong> - Professional synthetic turf and artificial grass installation
              serving Boston, Cambridge, Somerville, Brookline, Back Bay, South End, and surrounding areas.
              Licensed and insured contractors specializing in pet-friendly turf, putting greens, and low-maintenance
              landscape solutions for urban properties.
            </p>
          </div>
        </div>
      </section>
    </>
  );
};

export default SyntheticTurfInstallation;
