import { BrowserRouter, Routes, Route } from 'react-router-dom';
import Header from './components/common/Header';
import Footer from './components/common/Footer';

// Import page components (we'll create these)
import Homepage from './pages/Homepage';
import NotFound from './pages/NotFound';

// Category pages
import UrbanLandscapeDesign from './pages/categories/UrbanLandscapeDesign';
import CustomHardscapingPatios from './pages/categories/CustomHardscapingPatios';
import SyntheticTurfInstallation from './pages/categories/SyntheticTurfInstallation';
import OutdoorLivingFeatures from './pages/categories/OutdoorLivingFeatures';

// Urban Landscape Design services
import OutdoorLivingSpaceDesign from './pages/services/urban-landscape-design/OutdoorLivingSpaceDesign';
import UrbanBackyardTransformation from './pages/services/urban-landscape-design/UrbanBackyardTransformation';
import SmallYardLandscapeDesignBuild from './pages/services/urban-landscape-design/SmallYardLandscapeDesignBuild';
import HighEndLandscapeDesign from './pages/services/urban-landscape-design/HighEndLandscapeDesign';
import ModernLandscapeDesign from './pages/services/urban-landscape-design/ModernLandscapeDesign';

// Custom Hardscaping services
import CustomPatioInstallation from './pages/services/custom-hardscaping-patios/CustomPatioInstallation';
import LuxuryHardscapeDesign from './pages/services/custom-hardscaping-patios/LuxuryHardscapeDesign';
import FirePitPizzaOvenInstallation from './pages/services/custom-hardscaping-patios/FirePitPizzaOvenInstallation';
import CustomFirePits from './pages/services/custom-hardscaping-patios/CustomFirePits';
import WalkwayPathwayInstallation from './pages/services/custom-hardscaping-patios/WalkwayPathwayInstallation';
import RetainingWallConstruction from './pages/services/custom-hardscaping-patios/RetainingWallConstruction';

// Synthetic Turf services
import ArtificialGrassSmallYards from './pages/services/synthetic-turf-installation/ArtificialGrassSmallYards';
import SyntheticGrassInstallation from './pages/services/synthetic-turf-installation/SyntheticGrassInstallation';
import PetFriendlyArtificialTurf from './pages/services/synthetic-turf-installation/PetFriendlyArtificialTurf';
import PuttingGreenInstallation from './pages/services/synthetic-turf-installation/PuttingGreenInstallation';

// Outdoor Living services
import OutdoorKitchenInstallation from './pages/services/outdoor-living-features/OutdoorKitchenInstallation';
import OutdoorLightingInstallation from './pages/services/outdoor-living-features/OutdoorLightingInstallation';
import WaterFeatureInstallation from './pages/services/outdoor-living-features/WaterFeatureInstallation';
import PergolaTrellisInstallation from './pages/services/outdoor-living-features/PergolaTrellisInstallation';
import DeckPatioCoverInstallation from './pages/services/outdoor-living-features/DeckPatioCoverInstallation';

// Location pages
import LandscapeDesignerBackBay from './pages/locations/boston/LandscapeDesignerBackBay';
import LandscapeDesignerSouthEnd from './pages/locations/boston/LandscapeDesignerSouthEnd';
import LandscapeDesignerCambridge from './pages/locations/cambridge/LandscapeDesigner';
import LandscapeDesignerSomerville from './pages/locations/somerville/LandscapeDesigner';
import LandscapeDesignerBrookline from './pages/locations/brookline/LandscapeDesigner';
import LandscapeDesignerArlington from './pages/locations/arlington/LandscapeDesigner';
import LandscapeDesignerWinchester from './pages/locations/winchester/LandscapeDesigner';
import LandscapeDesignerMedford from './pages/locations/medford/LandscapeDesigner';

// Additional pages
import About from './pages/About';
import Contact from './pages/Contact';
import Blog from './pages/Blog';

function App() {
  return (
    <BrowserRouter basename="/">
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-grow">
          <Routes>
            {/* Homepage */}
            <Route path="/" element={<Homepage />} />

            {/* Category Pages */}
            <Route path="/urban-landscape-design" element={<UrbanLandscapeDesign />} />
            <Route path="/custom-hardscaping-patios" element={<CustomHardscapingPatios />} />
            <Route path="/synthetic-turf-installation" element={<SyntheticTurfInstallation />} />
            <Route path="/outdoor-living-features" element={<OutdoorLivingFeatures />} />

            {/* Urban Landscape Design Services */}
            <Route path="/urban-landscape-design/outdoor-living-space-design" element={<OutdoorLivingSpaceDesign />} />
            <Route path="/urban-landscape-design/urban-backyard-transformation" element={<UrbanBackyardTransformation />} />
            <Route path="/urban-landscape-design/small-yard-landscape-design-build" element={<SmallYardLandscapeDesignBuild />} />
            <Route path="/urban-landscape-design/high-end-landscape-design" element={<HighEndLandscapeDesign />} />
            <Route path="/urban-landscape-design/modern-landscape-design" element={<ModernLandscapeDesign />} />

            {/* Custom Hardscaping Services */}
            <Route path="/custom-hardscaping-patios/custom-patio-installation" element={<CustomPatioInstallation />} />
            <Route path="/custom-hardscaping-patios/luxury-hardscape-design" element={<LuxuryHardscapeDesign />} />
            <Route path="/custom-hardscaping-patios/fire-pit-pizza-oven-installation" element={<FirePitPizzaOvenInstallation />} />
            <Route path="/custom-hardscaping-patios/custom-fire-pits" element={<CustomFirePits />} />
            <Route path="/custom-hardscaping-patios/walkway-pathway-installation" element={<WalkwayPathwayInstallation />} />
            <Route path="/custom-hardscaping-patios/retaining-wall-construction" element={<RetainingWallConstruction />} />

            {/* Synthetic Turf Services */}
            <Route path="/synthetic-turf-installation/artificial-grass-small-yards" element={<ArtificialGrassSmallYards />} />
            <Route path="/synthetic-turf-installation/synthetic-grass-installation" element={<SyntheticGrassInstallation />} />
            <Route path="/synthetic-turf-installation/pet-friendly-artificial-turf" element={<PetFriendlyArtificialTurf />} />
            <Route path="/synthetic-turf-installation/putting-green-installation" element={<PuttingGreenInstallation />} />

            {/* Outdoor Living Services */}
            <Route path="/outdoor-living-features/outdoor-kitchen-installation" element={<OutdoorKitchenInstallation />} />
            <Route path="/outdoor-living-features/outdoor-lighting-installation" element={<OutdoorLightingInstallation />} />
            <Route path="/outdoor-living-features/water-feature-installation" element={<WaterFeatureInstallation />} />
            <Route path="/outdoor-living-features/pergola-trellis-installation" element={<PergolaTrellisInstallation />} />
            <Route path="/outdoor-living-features/deck-patio-cover-installation" element={<DeckPatioCoverInstallation />} />

            {/* Location Pages */}
            <Route path="/locations/boston/landscape-designer-back-bay" element={<LandscapeDesignerBackBay />} />
            <Route path="/locations/boston/landscape-designer-south-end" element={<LandscapeDesignerSouthEnd />} />
            <Route path="/locations/cambridge/landscape-designer" element={<LandscapeDesignerCambridge />} />
            <Route path="/locations/somerville/landscape-designer" element={<LandscapeDesignerSomerville />} />
            <Route path="/locations/brookline/landscape-designer" element={<LandscapeDesignerBrookline />} />
            <Route path="/locations/arlington/landscape-designer" element={<LandscapeDesignerArlington />} />
            <Route path="/locations/winchester/landscape-designer" element={<LandscapeDesignerWinchester />} />
            <Route path="/locations/medford/landscape-designer" element={<LandscapeDesignerMedford />} />

            {/* Additional Pages */}
            <Route path="/about" element={<About />} />
            <Route path="/contact" element={<Contact />} />
            <Route path="/blog" element={<Blog />} />

            {/* 404 Page */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </main>
        <Footer />
      </div>
    </BrowserRouter>
  );
}

export default App;
