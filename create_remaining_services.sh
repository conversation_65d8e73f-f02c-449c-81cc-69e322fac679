#!/bin/bash

# Synthetic Turf Services
services=(
  "SyntheticGrassInstallation:Synthetic Grass Installation Boston"
  "PetFriendlyArtificialTurf:Pet Friendly Artificial Turf Boston"
  "PuttingGreenInstallation:Putting Green Installation Boston"
)

for service in "${services[@]}"; do
  IFS=':' read -r filename title <<< "$service"
  cat > "src/pages/services/synthetic-turf-installation/${filename}.tsx" << EOFILE
import { createPlaceholderPage } from '../../../utils/createPlaceholderPage';

const ${filename} = createPlaceholderPage({
  title: "${title} | American Elm Landscape",
  description: "Professional ${title,,} services in Boston area.",
  keywords: "${title,,}, synthetic turf Boston",
  heroTitle: "${title}",
  heroSubtitle: "Professional ${title} Services",
  heroImage: "/images/hero-${filename,,}.jpg"
});

export default ${filename};
EOFILE
done

# Outdoor Living Services
outdoor_services=(
  "OutdoorKitchenInstallation:Outdoor Kitchen Installation Boston"
  "OutdoorLightingInstallation:Outdoor Lighting Installation Boston"
  "WaterFeatureInstallation:Water Feature Installation Boston"
  "PergolaTrellisInstallation:Pergola Trellis Installation Boston"
  "DeckPatioCoverInstallation:Deck Patio Cover Installation Boston"
)

for service in "${outdoor_services[@]}"; do
  IFS=':' read -r filename title <<< "$service"
  cat > "src/pages/services/outdoor-living-features/${filename}.tsx" << EOFILE
import { createPlaceholderPage } from '../../../utils/createPlaceholderPage';

const ${filename} = createPlaceholderPage({
  title: "${title} | American Elm Landscape",
  description: "Professional ${title,,} services in Boston area.",
  keywords: "${title,,}, outdoor living features Boston",
  heroTitle: "${title}",
  heroSubtitle: "Professional ${title} Services",
  heroImage: "/images/hero-${filename,,}.jpg"
});

export default ${filename};
EOFILE
done

echo "Created remaining service pages"
