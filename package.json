{"name": "american-elm-site", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"autoprefixer": "^10.4.21", "framer-motion": "^12.23.22", "lucide-react": "^0.544.0", "postcss": "^8.5.6", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router-dom": "^7.9.3", "tailwindcss": "^3.4.18"}, "devDependencies": {"@eslint/js": "^9.36.0", "@types/node": "^24.6.0", "@types/react": "^19.2.0", "@types/react-dom": "^19.2.0", "@vitejs/plugin-react": "^5.0.4", "eslint": "^9.36.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.22", "globals": "^16.4.0", "typescript": "~5.9.3", "typescript-eslint": "^8.45.0", "vite": "^7.1.7"}}