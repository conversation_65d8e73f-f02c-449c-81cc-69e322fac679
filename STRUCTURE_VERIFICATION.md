# American Elm Landscape - Site Structure Verification

## <PERSON> Core 30/40 Structure Compliance

This document verifies that the American Elm Landscape website follows <PERSON>'s Core 30/40 Website Structure methodology, which requires a "perfect mirror" of the Google Business Profile (GBP) structure.

---

## Three-Layer Hierarchy

### Layer 1: Homepage
- **URL**: `/`
- **File**: `src/pages/Homepage.tsx`
- **Title**: "Landscape Designer Boston"
- **Purpose**: Primary landing page targeting main business keyword

### Layer 2: Category Pages (4 Categories)

| Category | URL | File | Title |
|----------|-----|------|-------|
| Urban Landscape Design | `/urban-landscape-design` | `src/pages/categories/UrbanLandscapeDesign.tsx` | "Urban Landscape Design Boston" |
| Custom Hardscaping & Patios | `/custom-hardscaping-patios` | `src/pages/categories/CustomHardscapingPatios.tsx` | "Custom Hardscaping & Patios Boston" |
| Synthetic Turf Installation | `/synthetic-turf-installation` | `src/pages/categories/SyntheticTurfInstallation.tsx` | "Synthetic Turf Installation Boston" |
| Outdoor Living Features | `/outdoor-living-features` | `src/pages/categories/OutdoorLivingFeatures.tsx` | "Outdoor Living Features Boston" |

### Layer 3: Service Pages (20 Services)

#### Urban Landscape Design Services (5)
| Service | URL | File |
|---------|-----|------|
| Outdoor Living Space Design Boston | `/urban-landscape-design/outdoor-living-space-design` | `src/pages/services/urban-landscape-design/OutdoorLivingSpaceDesign.tsx` |
| Urban Backyard Transformation Boston | `/urban-landscape-design/urban-backyard-transformation` | `src/pages/services/urban-landscape-design/UrbanBackyardTransformation.tsx` |
| Small Yard Landscape Design-Build Boston | `/urban-landscape-design/small-yard-landscape-design-build` | `src/pages/services/urban-landscape-design/SmallYardLandscapeDesignBuild.tsx` |
| High-End Landscape Design Boston | `/urban-landscape-design/high-end-landscape-design` | `src/pages/services/urban-landscape-design/HighEndLandscapeDesign.tsx` |
| Modern Landscape Design Boston | `/urban-landscape-design/modern-landscape-design` | `src/pages/services/urban-landscape-design/ModernLandscapeDesign.tsx` |

#### Custom Hardscaping & Patios Services (6)
| Service | URL | File |
|---------|-----|------|
| Custom Patio Installation Boston | `/custom-hardscaping-patios/custom-patio-installation` | `src/pages/services/custom-hardscaping-patios/CustomPatioInstallation.tsx` |
| Luxury Hardscape Design Boston | `/custom-hardscaping-patios/luxury-hardscape-design` | `src/pages/services/custom-hardscaping-patios/LuxuryHardscapeDesign.tsx` |
| Fire Pit & Pizza Oven Installation Boston | `/custom-hardscaping-patios/fire-pit-pizza-oven-installation` | `src/pages/services/custom-hardscaping-patios/FirePitPizzaOvenInstallation.tsx` |
| Custom Fire Pits Boston | `/custom-hardscaping-patios/custom-fire-pits` | `src/pages/services/custom-hardscaping-patios/CustomFirePits.tsx` |
| Walkway & Pathway Installation Boston | `/custom-hardscaping-patios/walkway-pathway-installation` | `src/pages/services/custom-hardscaping-patios/WalkwayPathwayInstallation.tsx` |
| Retaining Wall Construction Boston | `/custom-hardscaping-patios/retaining-wall-construction` | `src/pages/services/custom-hardscaping-patios/RetainingWallConstruction.tsx` |

#### Synthetic Turf Installation Services (4)
| Service | URL | File |
|---------|-----|------|
| Artificial Grass for Small Yards Boston | `/synthetic-turf-installation/artificial-grass-small-yards` | `src/pages/services/synthetic-turf-installation/ArtificialGrassSmallYards.tsx` |
| Synthetic Grass Installation Boston | `/synthetic-turf-installation/synthetic-grass-installation` | `src/pages/services/synthetic-turf-installation/SyntheticGrassInstallation.tsx` |
| Pet-Friendly Artificial Turf Boston | `/synthetic-turf-installation/pet-friendly-artificial-turf` | `src/pages/services/synthetic-turf-installation/PetFriendlyArtificialTurf.tsx` |
| Putting Green Installation Boston | `/synthetic-turf-installation/putting-green-installation` | `src/pages/services/synthetic-turf-installation/PuttingGreenInstallation.tsx` |

#### Outdoor Living Features Services (5)
| Service | URL | File |
|---------|-----|------|
| Outdoor Kitchen Installation Boston | `/outdoor-living-features/outdoor-kitchen-installation` | `src/pages/services/outdoor-living-features/OutdoorKitchenInstallation.tsx` |
| Outdoor Lighting Installation Boston | `/outdoor-living-features/outdoor-lighting-installation` | `src/pages/services/outdoor-living-features/OutdoorLightingInstallation.tsx` |
| Water Feature Installation Boston | `/outdoor-living-features/water-feature-installation` | `src/pages/services/outdoor-living-features/WaterFeatureInstallation.tsx` |
| Pergola & Trellis Installation Boston | `/outdoor-living-features/pergola-trellis-installation` | `src/pages/services/outdoor-living-features/PergolaTrellisInstallation.tsx` |
| Deck & Patio Cover Installation Boston | `/outdoor-living-features/deck-patio-cover-installation` | `src/pages/services/outdoor-living-features/DeckPatioCoverInstallation.tsx` |

---

## Geo-Relevance Pages (8 Location Pages)

| Location | URL | File |
|----------|-----|------|
| Landscape Designer Back Bay Boston | `/locations/boston/landscape-designer-back-bay` | `src/pages/locations/boston/LandscapeDesignerBackBay.tsx` |
| Landscape Designer South End Boston | `/locations/boston/landscape-designer-south-end` | `src/pages/locations/boston/LandscapeDesignerSouthEnd.tsx` |
| Landscape Designer Cambridge | `/locations/cambridge/landscape-designer` | `src/pages/locations/cambridge/LandscapeDesigner.tsx` |
| Landscape Designer Somerville | `/locations/somerville/landscape-designer` | `src/pages/locations/somerville/LandscapeDesigner.tsx` |
| Landscape Designer Brookline | `/locations/brookline/landscape-designer` | `src/pages/locations/brookline/LandscapeDesigner.tsx` |
| Landscape Designer Arlington | `/locations/arlington/landscape-designer` | `src/pages/locations/arlington/LandscapeDesigner.tsx` |
| Landscape Designer Winchester | `/locations/winchester/landscape-designer` | `src/pages/locations/winchester/LandscapeDesigner.tsx` |
| Landscape Designer Medford | `/locations/medford/landscape-designer` | `src/pages/locations/medford/LandscapeDesigner.tsx` |

---

## Key Structure Principles

### 1. File Organization vs. URL Structure
- **File Structure**: Uses `/services/` directory for code organization
  - Example: `src/pages/services/urban-landscape-design/OutdoorLivingSpaceDesign.tsx`
- **URL Structure**: Does NOT include `/services/` for SEO optimization
  - Example: `/urban-landscape-design/outdoor-living-space-design`

### 2. Service + City Name Formula
Every service page targets the exact phrase: **Service + City Name**
- Example: "Custom Patio Installation Boston"
- Example: "Outdoor Kitchen Installation Boston"

### 3. Hierarchical Nesting
- Category pages group related services
- Service pages are nested under their parent category
- URLs reflect this logical hierarchy
- Google can easily understand the relationship between pages

### 4. GBP Mirror Structure
The website structure perfectly mirrors the Google Business Profile:
- Primary business category → Homepage
- Secondary categories → Layer 2 Category Pages
- Individual services → Layer 3 Service Pages

---

## SEO Implementation

### Canonical URLs
All canonical URLs follow the clean structure without `/services/`:
```
https://americanelmlandscape.com/urban-landscape-design/outdoor-living-space-design
https://americanelmlandscape.com/custom-hardscaping-patios/custom-patio-installation
```

### Breadcrumb Schema
Breadcrumbs reflect the three-layer hierarchy:
```
Home > Urban Landscape Design > Outdoor Living Space Design Boston
Home > Custom Hardscaping & Patios > Custom Patio Installation Boston
```

### Internal Linking
- Homepage links to all 4 category pages
- Category pages link to their respective service pages
- Service pages link to related services within the same category
- Cross-linking between related categories where relevant

---

## Compliance Checklist

✅ Three-layer hierarchy (Homepage → Category → Service)  
✅ Clean URL structure without `/services/` prefix  
✅ Service + City Name targeting on all service pages  
✅ Logical nesting of services under categories  
✅ Canonical URLs match URL structure  
✅ Breadcrumb schema matches hierarchy  
✅ Internal linking follows hub-and-spoke model  
✅ File organization maintains code clarity  
✅ GBP structure mirrored in website architecture  
✅ Location pages for geo-relevance  

---

## Status: FULLY COMPLIANT WITH CALEB ULKU CORE 30/40 STRUCTURE ✅

