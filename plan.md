Website Plan for American Elm Landscape
This plan outlines a complete, SEO-optimized website for American Elm Landscape, built as a high-end urban design-and-build firm targeting the Greater Boston area. The structure follows the "Perfect Mirror" hierarchy from the provided CSV and strategy: a three-layer setup (Homepage → Category Pages → Service Pages) plus dedicated Geo-Relevance location pages. This ensures exact alignment with GBP categories/services for Map Pack ranking.
Key principles:

Tech Stack: React (with hooks and context for state), TypeScript (for type safety), Vite (for fast dev/build), Tailwind CSS (for responsive styling with dark green as primary: #1a3c34 for accents, backgrounds; neutrals like #f8fafc for light mode). Add React Router for SPA routing, Framer Motion for subtle animations (e.g., fade-ins on scroll).
Design Theme: Modern, luxurious urban vibe—clean lines, high-contrast typography (serif for headings like Playfair Display, sans-serif body like Inter). Hero sections feature full-width images from the existing site (e.g., lush urban backyards). Mobile-first, accessible (ARIA labels, alt text for all images).
SEO & Internal Linking:

Every page mirrors GBP terminology (e.g., H1 = page title like "Urban Landscape Design Boston").
Internal links: Homepage embeds plain-text links under H2s to Layer 2 pages; Layer 2 uses H2s with descriptive paragraphs linking to Layer 3; all pages link back to Homepage and cross-link to related Geo-Relevance pages.
Content: 1500+ words per Layer 2, 800+ per Layer 3 (focus on local relevance: Boston's dense lots, Cambridge's historic codes, weather-resilient designs). Use schema markup (JSON-LD for LocalBusiness, Service).
Media Reuse: Pull all images/videos from https://americanelmlandscape.com/ (e.g., hero: urban patio installs; galleries: turf transformations). Optimize with lazy-loading via next/image-like in React (or react-lazyload). Alt text: Keyword-rich, e.g., "Custom fire pit installation in Back Bay Boston".


Performance: Vite for HMR; bundle analysis; PWA-ready (manifest.json).
Deployment: Vercel/Netlify for easy CI/CD; Google Analytics/Search Console integration.

Project Structure
Organize as a monorepo for scalability. Use Vite's React TS template.
textamerican-elm-site/
├── public/
│   ├── favicon.ico
│   ├── robots.txt
│   └── images/  # Static assets from existing site (download all ~50 images: heroes, galleries, logos)
│       ├── hero-urban-backyard.jpg  # Reused from current homepage
│       ├── gallery-patio-install.jpg  # From services gallery
│       └── ... (all media: 20+ turf, 15+ hardscape, 10+ features, 5+ geo-specific if available)
├── src/
│   ├── components/  # Reusable UI
│   │   ├── common/
│   │   │   ├── Header.tsx  # Nav with dark green bg, mobile hamburger
│   │   │   ├── Footer.tsx  # Links to all layers + contact; dark green accents
│   │   │   ├── Hero.tsx  # Full-bleed image + overlay H1/CTA; animated scroll
│   │   │   └── SEO.tsx  # Helmet for titles/meta (e.g., "Landscape Designer Boston | High-End Urban Design")
│   │   ├── ui/
│   │   │   ├── Button.tsx  # CTA: "Schedule Consultation" in dark green
│   │   │   ├── Card.tsx  # For service teasers: image + title + link
│   │   │   └── Gallery.tsx  # Masonry grid with reused images; lightbox modal
│   │   └── forms/
│   │       └── ContactForm.tsx  # Integrated with Netlify Forms or EmailJS; fields: name, service, location
│   ├── pages/  # Routed pages
│   │   ├── index.tsx  # Layer 1: Homepage
│   │   ├── categories/
│   │   │   ├── UrbanLandscapeDesign.tsx  # Layer 2
│   │   │   ├── CustomHardscapingPatios.tsx
│   │   │   ├── SyntheticTurfInstallation.tsx
│   │   │   └── OutdoorLivingFeatures.tsx
│   │   ├── services/
│   │   │   ├── urban-landscape-design/
│   │   │   │   ├── OutdoorLivingSpaceDesign.tsx  # Layer 3
│   │   │   │   ├── UrbanBackyardTransformation.tsx
│   │   │   │   ├── SmallYardLandscapeDesignBuild.tsx
│   │   │   │   ├── HighEndLandscapeDesign.tsx
│   │   │   │   └── ModernLandscapeDesign.tsx
│   │   │   ├── custom-hardscaping-patios/
│   │   │   │   ├── CustomPatioInstallation.tsx
│   │   │   │   ├── LuxuryHardscapeDesign.tsx
│   │   │   │   ├── FirePitPizzaOvenInstallation.tsx
│   │   │   │   ├── CustomFirePits.tsx
│   │   │   │   ├── WalkwayPathwayInstallation.tsx
│   │   │   │   └── RetainingWallConstruction.tsx
│   │   │   ├── synthetic-turf-installation/
│   │   │   │   ├── ArtificialGrassSmallYards.tsx
│   │   │   │   ├── SyntheticGrassInstallation.tsx
│   │   │   │   ├── PetFriendlyArtificialTurf.tsx
│   │   │   │   └── PuttingGreenInstallation.tsx
│   │   │   └── outdoor-living-features/
│   │   │       ├── OutdoorKitchenInstallation.tsx
│   │   │       ├── OutdoorLightingInstallation.tsx
│   │   │       ├── WaterFeatureInstallation.tsx
│   │   │       ├── PergolaTrellisInstallation.tsx
│   │   │       └── DeckPatioCoverInstallation.tsx
│   │   └── locations/
│   │       ├── boston/
│   │       │   ├── LandscapeDesignerBackBay.tsx  # Geo-Relevance
│   │       │   └── LandscapeDesignerSouthEnd.tsx
│   │       ├── cambridge/
│   │       │   └── LandscapeDesigner.tsx
│   │       ├── somerville/
│   │       │   └── LandscapeDesigner.tsx
│   │       ├── brookline/
│   │       │   └── LandscapeDesigner.tsx
│   │       ├── arlington/
│   │       │   └── LandscapeDesigner.tsx
│   │       ├── winchester/
│   │       │   └── LandscapeDesigner.tsx
│   │       └── medford/
│   │           └── LandscapeDesigner.tsx
│   ├── hooks/  # Custom: useMediaQuery for responsive
│   ├── utils/  # Constants: colors { primary: '#1a3c34' }, schema markup helpers
│   ├── App.tsx  # Router setup
│   ├── main.tsx  # Vite entry
│   └── vite-env.d.ts
├── tailwind.config.js  # Extend with dark green theme
├── vite.config.ts  # Plugins: react, vite-plugin-pwa
├── tsconfig.json
├── package.json  # Deps: react-router-dom@^6, framer-motion@^10, @types/react, tailwindcss
└── README.md  # Build/deploy notes
Routing Setup (React Router)
In App.tsx:
tsximport { BrowserRouter, Routes, Route } from 'react-router-dom';
// ... imports for pages

function App() {
  return (
    <BrowserRouter basename="/">
      <Header />
      <Routes>
        <Route path="/" element={<Homepage />} />
        <Route path="/urban-landscape-design" element={<UrbanLandscapeDesign />} />
        <Route path="/custom-hardscaping-patios" element={<CustomHardscapingPatios />} />
        {/* ... all Layer 2 */}
        <Route path="/urban-landscape-design/outdoor-living-space-design" element={<OutdoorLivingSpaceDesign />} />
        {/* ... all Layer 3 (20+ routes) */}
        <Route path="/locations/boston/landscape-designer-back-bay" element={<LandscapeDesignerBackBay />} />
        {/* ... all Geo (8 routes) */}
        <Route path="*" element={<NotFound />} />  {/* 404 with links back to home */}
      </Routes>
      <Footer />
    </BrowserRouter>
  );
}

Use Link components for all internal navigation (e.g., <Link to="/urban-landscape-design">Explore Urban Design</Link>).

Page Templates & Content Outline
All pages share <Layout> wrapper: Hero + Content + Gallery + CTA Form + Footer. Reuse media: Assign 3-5 images per page from existing site (e.g., turf pages use artificial grass installs).

Layer 1: Homepage (/) - "Landscape Designer Boston"

Hero: Full-width image (reused urban yard transformation). H1: "Landscape Designer Boston | High-End Urban Design & Build". CTA: "Get Quote".
Content: 2000+ words. Intro para with primary KW. H2 sections (one per Layer 2 category):

H2: "Urban Landscape Design Boston" → 80-word para on small-lot transformations → Link to /urban-landscape-design.
H2: "Custom Hardscaping & Patios Boston" → Para on luxury installs → Link.
H2: "Synthetic Turf Installation Boston" → Para on low-maint solutions → Link.
H2: "Outdoor Living Features Boston" → Para on features → Link.
H2: "Serving Greater Boston's Finest Neighborhoods" → Links to all Geo pages (e.g., "Back Bay Landscape Designer").


Gallery: 6-8 reused images (mixed services).
Internal Links: 10+ embedded in content; nav bar to Layer 2.
Schema: LocalBusiness with address (Medford base), services array.


Layer 2: Category Pages (e.g., /urban-landscape-design - "Urban Landscape Design Boston")

Hero: Category-specific image (reused modern design). H1: Exact title.
Content: 1500 words hub. Intro on Greater Boston focus (mention tiers: Core like Cambridge). H2 per Layer 3 service:

H2: "Outdoor Living Space Design Boston" → 100-word para (local: "Tailored for South End rowhouses") → Link to /urban-landscape-design/outdoor-living-space-design.
Repeat for 4-6 services under category.


Cross-Links: Sidebar widget: "Related Locations" → Links to Geo pages (e.g., Cambridge).
Gallery: 4-6 images from existing (e.g., before/after backyards).
CTA: Form pre-filled with category.


Layer 3: Service Pages (e.g., /urban-landscape-design/outdoor-living-space-design - "Outdoor Living Space Design Boston")

Hero: Service image (reused outdoor space). H1: Exact title.
Content: 1000 words. Sections: Process (design-build steps), Benefits (high-end focus), Local Tips (e.g., "Boston zoning for small yards"). Embed 2-3 H3s with KW variations.
Internal Links: Breadcrumbs (Home > Urban Design > Outdoor Spaces); "Related Services" cards linking sibling Layer 3; "In Your Area?" → Geo links.
Gallery: 5+ reused images (process shots).
Schema: Service type with offers pricingRange="$$  $-  $$$$".


Geo-Relevance Pages (e.g., /locations/boston/landscape-designer-back-bay - "Landscape Designer Back Bay Boston")

Hero: Location image (reused Boston urban; if none, generic + overlay text).
Content: 800 words. H1: Exact title. Focus: Neighborhood specifics (e.g., "Victorian brownstones in Back Bay demand compact, elegant designs"). List 3-5 services with links to Layer 3 (e.g., "Custom Patios for Back Bay").
Internal Links: "Our Services in Back Bay" → All relevant Layer 3; "Nearby Areas" → Other Geo.
Gallery: 3-4 images tagged to Boston if available.
Tier Alignment: Core tiers (Boston/Cambridge) get more content/links; Tier 3 (Medford) minimal but NAP-consistent.



Additional Pages & Features

About: /about - Firm story (Allan’s vision), team bios, 1000 words. Links to all layers. Reused headshots.
Contact: /contact - Map (Google embed for Medford), form, hours. Links to Geo for "Find Us Near You".
Blog: /blog - Future-proof; stub with 2 posts on trends (e.g., "Sustainable Turf in Somerville"). Link from footer.
Global Components:

Nav: Sticky, dark green; dropdowns for categories (Layer 2) and locations.
Footer: Sitemap links (all URLs), social (reuse icons), NAP.


Animations: Subtle (e.g., cards lift on hover); no overload.

Implementation Steps

Setup: npm create vite@latest -- --template react-ts. Add deps: npm i react-router-dom framer-motion tailwindcss postcss autoprefixer. Init Tailwind.
Style Config: tailwind.config.js:
jsmodule.exports = {
  content: ['./src/**/*.{ts,tsx}'],
  theme: { extend: { colors: { 'dark-green': '#1a3c34' } } },
  plugins: [],
};

Media Integration: Download all assets from current site (use wget or manual). Place in public/images/; reference as <img src="/images/hero-urban.jpg" alt="Urban backyard in Boston" />. Optimize: Add WebP via Vite plugin.
Build Content: Use MDX or static props for pages; populate with KW-focused copy (hire writer for uniqueness).
Test SEO: Lighthouse audits; ensure 100% mobile score. Add external link placeholders (e.g., comment for PBN).
Launch: npm run build; deploy. Monitor GBP sync post-launch.

This structure maximizes GBP mirroring, drives internal authority flow, and positions the site as a high-end hub. Total pages: ~35 (1 home + 4 cat + 20 serv + 8 geo + extras). Budget: 2-4 weeks dev time. Let me know if you need code snippets or wireframes!