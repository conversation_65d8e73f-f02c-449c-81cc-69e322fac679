#!/bin/bash

# Build out remaining location pages with comprehensive content

# Brookline
cat > src/pages/locations/brookline/LandscapeDesigner.tsx << 'EOF'
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import SEO from '../../../components/common/SEO';
import Hero from '../../../components/common/Hero';
import Card from '../../../components/ui/Card';
import ContactForm from '../../../components/forms/ContactForm';
import { createServiceSchema, createBreadcrumbSchema } from '../../../utils/schema';
import { landscapeImages } from '../../../utils/images';

const LandscapeDesignerBrookline: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://americanelmlandscape.com/' },
    { name: 'Locations', url: 'https://americanelmlandscape.com/locations' },
    { name: 'Landscape Designer Brookline', url: 'https://americanelmlandscape.com/locations/brookline/landscape-designer' }
  ];

  const brooklineServices = [
    {
      name: 'Coolidge Corner Landscaping',
      description: 'Sophisticated landscape design for Brookline\'s vibrant Coolidge Corner area, blending urban convenience with suburban elegance.',
      link: '/services/urban-landscape-design/high-end-landscape-design'
    },
    {
      name: 'Luxury Estate Landscaping',
      description: 'High-end landscape design for Brookline\'s prestigious properties, featuring custom hardscaping and premium materials.',
      link: '/services/urban-landscape-design/luxury-landscape-design'
    },
    {
      name: 'Historic Property Restoration',
      description: 'Specialized landscaping for Brookline\'s historic homes, preserving character while adding modern functionality.',
      link: '/services/urban-landscape-design/historic-property-landscaping'
    },
    {
      name: 'Custom Patio & Hardscaping',
      description: 'Premium patio installation and hardscaping designed for Brookline\'s upscale residential properties.',
      link: '/services/custom-hardscaping-patios/luxury-hardscape-design'
    },
    {
      name: 'Outdoor Kitchen Design',
      description: 'Luxury outdoor kitchens perfect for Brookline\'s entertaining culture and year-round outdoor living.',
      link: '/services/outdoor-living-features/outdoor-kitchen-installation'
    },
    {
      name: 'Synthetic Turf Installation',
      description: 'Premium artificial grass solutions for Brookline families seeking low-maintenance, beautiful lawns.',
      link: '/services/synthetic-turf-installation/synthetic-grass-installation'
    }
  ];

  const nearbyAreas = [
    { name: 'Boston', path: '/locations/boston/landscape-designer-back-bay' },
    { name: 'Cambridge', path: '/locations/cambridge/landscape-designer' },
    { name: 'Newton', path: '/locations/newton/landscape-designer' },
    { name: 'Jamaica Plain', path: '/locations/boston/landscape-designer-jamaica-plain' }
  ];

  return (
    <>
      <SEO
        title="Landscape Designer Brookline | Coolidge Corner & Luxury Estate Specialists | American Elm Landscape"
        description="Expert landscape design for Brookline, MA properties. Specializing in luxury estates, Coolidge Corner area, and historic property landscaping."
        keywords="landscape designer Brookline, Brookline landscaping, Coolidge Corner landscaping, luxury landscape design Brookline, historic property landscaping"
        canonical="https://americanelmlandscape.com/locations/brookline/landscape-designer"
        schema={[
          createServiceSchema(
            'Landscape Designer Brookline',
            'Professional landscape design services for Brookline, MA properties, including luxury estates and historic homes.',
            'Brookline, MA'
          ),
          createBreadcrumbSchema(breadcrumbs)
        ]}
      />

      <Hero
        title="Landscape Designer Brookline"
        subtitle="Luxury Landscape Design for Brookline's Finest Properties"
        backgroundImage={landscapeImages.hero.luxuryDesign}
        ctaText="Schedule Consultation"
        ctaLink="/contact"
        height="medium"
      />

      <section className="py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <nav className="text-sm">
            <Link to="/" className="text-dark-green hover:text-light-green">Home</Link>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-500">Locations</span>
            <span className="mx-2 text-gray-500">/</span>
            <span className="text-gray-700">Landscape Designer Brookline</span>
          </nav>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl font-serif font-bold text-dark-green mb-8"
            >
              Brookline's Premier Luxury Landscape Design
            </motion.h1>
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="prose prose-lg max-w-none"
            >
              <p className="text-lg text-gray-600 leading-relaxed mb-6">
                Brookline represents the pinnacle of suburban elegance in the Greater Boston area, 
                and American Elm Landscape delivers landscape design that matches this prestigious 
                community's high standards. From the bustling Coolidge Corner to the tree-lined 
                residential streets, we create outdoor spaces that enhance Brookline's sophisticated lifestyle.
              </p>
              
              <p className="text-lg text-gray-600 leading-relaxed">
                Our expertise in luxury landscape design, historic property restoration, and premium 
                hardscaping makes us the preferred choice for Brookline's discerning homeowners who 
                demand excellence in every detail.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl font-serif font-bold text-dark-green text-center mb-12"
          >
            Brookline Landscape Design Services
          </motion.h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {brooklineServices.map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card
                  title={service.name}
                  description={service.description}
                  link={service.link}
                  image={landscapeImages.gallery.patio}
                />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl font-serif font-bold text-dark-green text-center mb-8"
          >
            We Also Serve Nearby Areas
          </motion.h2>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex flex-wrap justify-center gap-4"
          >
            {nearbyAreas.map((area, index) => (
              <Link
                key={index}
                to={area.path}
                className="bg-white px-6 py-3 rounded-lg shadow-md hover:shadow-lg transition-shadow text-dark-green hover:text-light-green font-medium"
              >
                {area.name}
              </Link>
            ))}
          </motion.div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-serif font-bold text-dark-green mb-4">
                Ready to Transform Your Brookline Property?
              </h2>
              <p className="text-lg text-gray-600">
                Contact us today for a free consultation and discover how we can enhance your outdoor space.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <ContactForm />
            </motion.div>
          </div>
        </div>
      </section>
    </>
  );
};

export default LandscapeDesignerBrookline;
EOF

echo "Created comprehensive Brookline location page"
